# 代码质量Issue自动修复Agent

基于LangGraph实现的智能代码修复Agent，支持自动分析代码质量问题并生成修复方案，包含反思链验证机制。

## 功能特性

- 🤖 **智能分析**: 基于Claude Sonnet 3.7模型分析代码质量问题
- 🔧 **自动修复**: 生成具体的代码修复方案（行号、修改前后对比）
- 🔍 **反思验证**: 内置反思链对修复方案进行二次确认
- 📝 **详细日志**: 自动记录修复过程到`autofix_argument.log`
- 🎯 **规则支持**: 支持多种代码质量规则（FindBugs、SonarQube等）

## 系统架构

### LangGraph工作流

```mermaid
graph TD
    A[开始] --> B[分析Issues]
    B --> C{需要工具?}
    C -->|是| D[调用工具]
    C -->|否| E[生成修复方案]
    D --> E
    E --> F[验证修复方案]
    F --> G{验证通过?}
    G -->|否| H{重试次数<2?}
    H -->|是| E
    H -->|否| I[结束]
    G -->|是| J[应用修复]
    J --> I
```

### 核心组件

1. **工具 (Tools)**
   - `read_source_file`: 读取源代码文件
   - `get_rule_details`: 获取规则详细信息
   - `write_source_file`: 写入修复后的代码

2. **状态管理 (State)**
   - 文件路径和Issues列表
   - 原始代码和规则详情
   - 修复建议和验证结果

3. **节点 (Nodes)**
   - 分析节点：分析问题并获取规则信息
   - 修复节点：生成具体修复方案
   - 验证节点：反思链验证修复正确性
   - 应用节点：将修复应用到文件

## 安装和配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 设置API密钥

```bash
export OPENAI_API_KEY="your-openai-api-key"
```

### 3. 准备数据文件

确保以下文件存在：
- `data/issues.json`: 包含代码质量问题的JSON文件
- `data/rules_origin.json`: 包含规则详细信息的JSON文件

## 使用方法

### 处理所有文件

```bash
python main.py
```

### 处理单个文件

```bash
python main.py "path/to/your/file.java"
```

### 运行测试

```bash
python test_agent.py
```

## 输入格式

### Issues JSON格式

```json
{
  "file/path.java": [
    {
      "rule": "squid:S2111",
      "location": "file/path.java",
      "line": 185,
      "textRange": {
        "startLine": 185,
        "endLine": 185,
        "startOffset": 0,
        "endOffset": 76
      },
      "message": "Use \"BigDecimal.valueOf\" instead."
    }
  ]
}
```

## 输出格式

### 修复方案格式

```json
[
  {
    "line": 181,
    "before": "BigDecimal bd = new BigDecimal(value);",
    "after": "BigDecimal bd = BigDecimal.valueOf(value);"
  }
]
```

### 日志格式

修复日志会自动保存到`autofix_argument.log`：

```json
{
  "file": "path/to/file.java",
  "fixes": [...],
  "timestamp": "2024-01-01T12:00:00"
}
```

## 支持的规则类型

- **FindBugs规则**: `findbugs:*`
  - `WMI_WRONG_MAP_ITERATOR`: Map迭代器效率问题
  - `DMI_BIGDECIMAL_CONSTRUCTED_FROM_DOUBLE`: BigDecimal构造问题
  - `UUF_UNUSED_FIELD`: 未使用字段
  - 等等...

- **SonarQube规则**: `squid:*`
  - `S2111`: BigDecimal构造函数问题
  - `custom:*`: 自定义规则
  - 等等...

## 配置选项

### Agent配置

```python
agent = CodeFixAgent(
    model_name="gpt-4o",  # 使用的模型
)
```

### 重试机制

- 最大重试次数：2次
- 验证失败时自动重试
- 超过重试次数后停止处理

## 最佳实践

1. **规则优先级**: 优先处理高风险的代码质量问题
2. **最小修改**: 保持修改范围最小化，避免影响其他代码
3. **验证机制**: 利用反思链确保修复方案的正确性
4. **日志记录**: 定期检查修复日志，确保修复质量

## 故障排除

### 常见问题

1. **API密钥错误**
   ```
   Error: OPENAI_API_KEY environment variable not set
   ```
   解决：设置正确的OpenAI API密钥

2. **文件读取失败**
   ```
   Error reading file: [Errno 2] No such file or directory
   ```
   解决：确保文件路径正确且文件存在

3. **JSON解析错误**
   ```
   Error parsing fix suggestions: Expecting value
   ```
   解决：检查模型输出格式，可能需要调整提示词

### 调试模式

启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 扩展开发

### 添加新工具

```python
@tool
def your_custom_tool(param: str) -> str:
    """工具描述"""
    # 实现逻辑
    return result
```

### 自定义验证规则

```python
def custom_validation(state: CodeFixState) -> Dict[str, Any]:
    # 自定义验证逻辑
    return validation_result
```

## 项目文件结构

```
autofix/
├── code_fix_agent.py          # 核心Agent实现
├── main.py                    # 主程序入口
├── demo.py                    # 功能演示
├── test_agent.py              # 测试脚本
├── monitor.py                 # 性能监控工具
├── visualize_graph.py         # 工作流可视化
├── requirements.txt           # 依赖包列表
├── .env.example              # 环境变量示例
├── README.md                 # 项目说明
├── data/
│   ├── issues.json           # 代码质量问题数据
│   └── rules_origin.json     # 规则详细信息
├── demo_files/               # 演示文件目录
├── test_files/               # 测试文件目录
└── autofix_argument.log      # 修复日志（自动生成）
```

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd autofix

# 安装依赖
pip install -r requirements.txt

# 设置API密钥
export OPENAI_API_KEY="your-openai-api-key"
```

### 2. 运行演示

```bash
# 运行功能演示
python demo.py

# 运行测试
python test_agent.py

# 可视化工作流
python visualize_graph.py
```

### 3. 处理实际项目

```bash
# 处理所有文件
python main.py

# 处理单个文件
python main.py "path/to/your/file.java"

# 监控性能
python monitor.py
```

## 高级功能

### 性能监控

```bash
# 生成分析报告
python monitor.py

# 实时监控
python monitor.py monitor
```

### 工作流可视化

```bash
# 生成工作流图
python visualize_graph.py
```

这将生成：
- `agent_workflow.png` - 工作流程图
- `agent_workflow.mmd` - Mermaid代码
- `workflow_documentation.md` - 详细文档

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持基本的代码质量问题修复
- 实现LangGraph工作流
- 添加反思链验证机制
- 支持多种规则类型
