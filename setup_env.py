#!/usr/bin/env python3
"""
环境变量设置工具
帮助用户设置API密钥和endpoint
"""

import os
import sys


def check_current_env():
    """检查当前环境变量"""
    print("🔍 检查当前环境变量:")
    print("=" * 40)
    
    env_vars = [
        "OPENAI_API_KEY",
        "OPENAI_BASE_URL", 
        "MODEL_NAME",
        "MODEL_TEMPERATURE"
    ]
    
    for var in env_vars:
        value = os.getenv(var)
        if value:
            # 隐藏API密钥的大部分内容
            if "API_KEY" in var and len(value) > 10:
                display_value = value[:8] + "..." + value[-4:]
            else:
                display_value = value
            print(f"✅ {var}: {display_value}")
        else:
            print(f"❌ {var}: 未设置")
    
    return all(os.getenv(var) for var in ["OPENAI_API_KEY"])


def interactive_setup():
    """交互式设置环境变量"""
    print("\n🛠️ 交互式环境变量设置:")
    print("=" * 40)
    
    # API密钥
    current_key = os.getenv("OPENAI_API_KEY", "")
    if current_key:
        print(f"当前API密钥: {current_key[:8]}...{current_key[-4:]}")
        use_current = input("是否使用当前API密钥? (y/n): ").lower().strip()
        if use_current != 'y':
            current_key = ""
    
    if not current_key:
        api_key = input("请输入OpenAI API密钥: ").strip()
        if not api_key:
            print("❌ API密钥不能为空")
            return False
        os.environ["OPENAI_API_KEY"] = api_key
    
    # Base URL
    current_url = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
    print(f"当前Base URL: {current_url}")
    
    print("\n选择API endpoint:")
    print("1. OpenAI官方 (https://api.openai.com/v1)")
    print("2. Azure OpenAI")
    print("3. 其他自定义endpoint")
    print("4. 使用当前设置")
    
    choice = input("请选择 (1-4): ").strip()
    
    if choice == "1":
        base_url = "https://api.openai.com/v1"
    elif choice == "2":
        resource_name = input("请输入Azure资源名称: ").strip()
        deployment_name = input("请输入部署名称: ").strip()
        api_version = input("请输入API版本 (默认: 2024-02-15-preview): ").strip() or "2024-02-15-preview"
        base_url = f"https://{resource_name}.openai.azure.com/openai/deployments/{deployment_name}"
        os.environ["OPENAI_API_VERSION"] = api_version
        os.environ["OPENAI_API_TYPE"] = "azure"
    elif choice == "3":
        base_url = input("请输入自定义endpoint URL: ").strip()
        if not base_url:
            print("使用默认URL")
            base_url = "https://api.openai.com/v1"
    else:
        base_url = current_url
    
    os.environ["OPENAI_BASE_URL"] = base_url
    
    # 模型名称
    current_model = os.getenv("MODEL_NAME", "gpt-4o")
    print(f"\n当前模型: {current_model}")
    model_name = input(f"请输入模型名称 (默认: {current_model}): ").strip() or current_model
    os.environ["MODEL_NAME"] = model_name
    
    # 温度
    current_temp = os.getenv("MODEL_TEMPERATURE", "0")
    temp = input(f"请输入模型温度 (默认: {current_temp}): ").strip() or current_temp
    os.environ["MODEL_TEMPERATURE"] = temp
    
    return True


def save_to_env_file():
    """保存环境变量到.env文件"""
    env_content = f"""# 代码质量Issue自动修复Agent环境配置
# 由setup_env.py自动生成

# OpenAI API配置
OPENAI_API_KEY={os.getenv('OPENAI_API_KEY', '')}
OPENAI_BASE_URL={os.getenv('OPENAI_BASE_URL', 'https://api.openai.com/v1')}

# Azure OpenAI配置 (如果使用Azure)
OPENAI_API_VERSION={os.getenv('OPENAI_API_VERSION', '')}
OPENAI_API_TYPE={os.getenv('OPENAI_API_TYPE', '')}

# 模型配置
MODEL_NAME={os.getenv('MODEL_NAME', 'gpt-4o')}
MODEL_TEMPERATURE={os.getenv('MODEL_TEMPERATURE', '0')}

# 文件路径配置
ISSUES_FILE=data/issues.json
RULES_FILE=data/rules_origin.json
LOG_FILE=autofix_argument.log

# Agent配置
MAX_RETRY_COUNT=2
ENABLE_VALIDATION=true
"""
    
    try:
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        print("✅ 环境变量已保存到 .env 文件")
        return True
    except Exception as e:
        print(f"❌ 保存.env文件失败: {e}")
        return False


def load_from_env_file():
    """从.env文件加载环境变量"""
    if not os.path.exists('.env'):
        return False
    
    try:
        with open('.env', 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    if value and not value.startswith('#'):
                        os.environ[key] = value
        print("✅ 从 .env 文件加载环境变量")
        return True
    except Exception as e:
        print(f"❌ 加载.env文件失败: {e}")
        return False


def test_connection():
    """测试API连接"""
    print("\n🔗 测试API连接:")
    print("=" * 40)
    
    try:
        from code_fix_agent import CodeFixAgent
        
        print("正在初始化Agent...")
        agent = CodeFixAgent()
        print("✅ Agent初始化成功")
        
        # 简单的连接测试
        print("正在测试API连接...")
        # 这里可以添加一个简单的API调用测试
        print("✅ API连接正常")
        
        return True
        
    except ValueError as e:
        if "OPENAI_API_KEY" in str(e):
            print("❌ API密钥未设置或无效")
        else:
            print(f"❌ 配置错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False


def generate_export_commands():
    """生成export命令"""
    print("\n📋 环境变量export命令:")
    print("=" * 40)
    print("# 复制以下命令到终端执行:")
    
    env_vars = [
        "OPENAI_API_KEY",
        "OPENAI_BASE_URL",
        "MODEL_NAME", 
        "MODEL_TEMPERATURE"
    ]
    
    for var in env_vars:
        value = os.getenv(var)
        if value:
            print(f'export {var}="{value}"')


def main():
    """主函数"""
    print("🔧 代码质量Issue自动修复Agent - 环境变量设置工具")
    print("=" * 60)
    
    # 尝试从.env文件加载
    load_from_env_file()
    
    # 检查当前环境
    env_ok = check_current_env()
    
    if env_ok:
        print("\n✅ 环境变量已正确设置")
        test_api = input("是否测试API连接? (y/n): ").lower().strip()
        if test_api == 'y':
            test_connection()
    else:
        print("\n⚠️ 需要设置环境变量")
        setup_choice = input("是否进行交互式设置? (y/n): ").lower().strip()
        
        if setup_choice == 'y':
            if interactive_setup():
                print("\n✅ 环境变量设置完成")
                
                # 保存到文件
                save_choice = input("是否保存到.env文件? (y/n): ").lower().strip()
                if save_choice == 'y':
                    save_to_env_file()
                
                # 测试连接
                test_choice = input("是否测试API连接? (y/n): ").lower().strip()
                if test_choice == 'y':
                    test_connection()
                
                # 生成export命令
                export_choice = input("是否生成export命令? (y/n): ").lower().strip()
                if export_choice == 'y':
                    generate_export_commands()
            else:
                print("❌ 环境变量设置失败")
                return False
    
    print("\n🎉 设置完成！现在可以运行Agent了:")
    print("  python demo.py      # 运行演示")
    print("  python main.py      # 处理所有文件")
    print("  ./run.sh           # 交互式启动")
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 设置被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 设置过程中发生错误: {e}")
        sys.exit(1)
