"""
Agent性能监控和统计工具
"""

import json
import os
import time
from datetime import datetime
from typing import Dict, List, Any
from collections import defaultdict, Counter


class AgentMonitor:
    """Agent性能监控器"""
    
    def __init__(self, log_file: str = "autofix_argument.log"):
        self.log_file = log_file
        self.stats = defaultdict(int)
        self.rule_stats = Counter()
        self.file_stats = defaultdict(list)
        self.timing_stats = []
    
    def load_logs(self) -> List[Dict[str, Any]]:
        """加载修复日志"""
        logs = []
        if not os.path.exists(self.log_file):
            return logs
        
        try:
            with open(self.log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip():
                        log_entry = json.loads(line.strip())
                        logs.append(log_entry)
        except Exception as e:
            print(f"Error loading logs: {e}")
        
        return logs
    
    def analyze_logs(self) -> Dict[str, Any]:
        """分析修复日志"""
        logs = self.load_logs()
        
        if not logs:
            return {"error": "No logs found"}
        
        # 基本统计
        total_files = len(set(log['file'] for log in logs))
        total_fixes = sum(len(log['fixes']) for log in logs)
        
        # 按规则统计
        rule_counts = Counter()
        for log in logs:
            for fix in log['fixes']:
                # 从修复内容中推断规则类型
                if 'BigDecimal' in str(fix):
                    rule_counts['BigDecimal相关'] += 1
                elif 'entrySet' in str(fix):
                    rule_counts['Map迭代器'] += 1
                elif 'L' in str(fix) and 'l' in str(fix):
                    rule_counts['Long字面量'] += 1
                else:
                    rule_counts['其他'] += 1
        
        # 按文件类型统计
        file_type_counts = Counter()
        for log in logs:
            file_ext = os.path.splitext(log['file'])[1]
            file_type_counts[file_ext] += 1
        
        # 时间分析
        timestamps = [datetime.fromisoformat(log['timestamp']) for log in logs]
        if timestamps:
            earliest = min(timestamps)
            latest = max(timestamps)
            duration = latest - earliest
        else:
            earliest = latest = duration = None
        
        return {
            "summary": {
                "total_files": total_files,
                "total_fixes": total_fixes,
                "total_sessions": len(logs),
                "avg_fixes_per_file": total_fixes / total_files if total_files > 0 else 0
            },
            "rule_distribution": dict(rule_counts),
            "file_type_distribution": dict(file_type_counts),
            "time_analysis": {
                "earliest": earliest.isoformat() if earliest else None,
                "latest": latest.isoformat() if latest else None,
                "duration": str(duration) if duration else None
            },
            "recent_activity": logs[-5:] if len(logs) >= 5 else logs
        }
    
    def generate_report(self) -> str:
        """生成分析报告"""
        analysis = self.analyze_logs()
        
        if "error" in analysis:
            return f"❌ {analysis['error']}"
        
        report = []
        report.append("📊 代码修复Agent性能报告")
        report.append("=" * 50)
        
        # 基本统计
        summary = analysis["summary"]
        report.append(f"📁 处理文件数: {summary['total_files']}")
        report.append(f"🔧 总修复数: {summary['total_fixes']}")
        report.append(f"📝 修复会话数: {summary['total_sessions']}")
        report.append(f"📈 平均每文件修复数: {summary['avg_fixes_per_file']:.2f}")
        report.append("")
        
        # 规则分布
        report.append("🎯 修复规则分布:")
        for rule, count in analysis["rule_distribution"].items():
            percentage = (count / summary['total_fixes']) * 100 if summary['total_fixes'] > 0 else 0
            report.append(f"  - {rule}: {count} ({percentage:.1f}%)")
        report.append("")
        
        # 文件类型分布
        report.append("📄 文件类型分布:")
        for file_type, count in analysis["file_type_distribution"].items():
            percentage = (count / summary['total_files']) * 100 if summary['total_files'] > 0 else 0
            report.append(f"  - {file_type or '无扩展名'}: {count} ({percentage:.1f}%)")
        report.append("")
        
        # 时间分析
        time_analysis = analysis["time_analysis"]
        if time_analysis["earliest"]:
            report.append("⏰ 时间分析:")
            report.append(f"  - 首次修复: {time_analysis['earliest']}")
            report.append(f"  - 最近修复: {time_analysis['latest']}")
            report.append(f"  - 活动时长: {time_analysis['duration']}")
            report.append("")
        
        # 最近活动
        recent = analysis["recent_activity"]
        if recent:
            report.append("🕒 最近活动:")
            for activity in recent[-3:]:  # 显示最近3个
                timestamp = activity['timestamp']
                file_name = os.path.basename(activity['file'])
                fix_count = len(activity['fixes'])
                report.append(f"  - {timestamp}: {file_name} ({fix_count} fixes)")
        
        return "\n".join(report)
    
    def save_report(self, filename: str = "agent_report.txt"):
        """保存报告到文件"""
        report = self.generate_report()
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"✅ 报告已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存报告失败: {e}")
    
    def real_time_monitor(self, interval: int = 5):
        """实时监控（简单版本）"""
        print("🔍 启动实时监控...")
        print(f"监控间隔: {interval}秒")
        print("按 Ctrl+C 停止监控")
        
        last_size = 0
        if os.path.exists(self.log_file):
            last_size = os.path.getsize(self.log_file)
        
        try:
            while True:
                time.sleep(interval)
                
                if os.path.exists(self.log_file):
                    current_size = os.path.getsize(self.log_file)
                    if current_size > last_size:
                        print(f"📝 {datetime.now().strftime('%H:%M:%S')} - 检测到新的修复活动")
                        last_size = current_size
                
        except KeyboardInterrupt:
            print("\n🛑 监控已停止")


def analyze_issues_file(issues_file: str = "data/issues.json") -> Dict[str, Any]:
    """分析issues.json文件"""
    try:
        with open(issues_file, 'r', encoding='utf-8') as f:
            issues_data = json.load(f)
    except Exception as e:
        return {"error": f"Failed to load {issues_file}: {e}"}
    
    # 统计分析
    total_files = len(issues_data)
    total_issues = sum(len(issues) for issues in issues_data.values())
    
    # 按规则统计
    rule_counts = Counter()
    severity_counts = Counter()
    
    for file_path, issues in issues_data.items():
        for issue in issues:
            rule = issue.get("rule", "unknown")
            rule_counts[rule] += 1
            
            # 简单的严重性分类
            if "findbugs" in rule.lower():
                severity_counts["FindBugs"] += 1
            elif "squid" in rule.lower():
                severity_counts["SonarQube"] += 1
            else:
                severity_counts["Other"] += 1
    
    # 文件分布
    issues_per_file = [len(issues) for issues in issues_data.values()]
    avg_issues_per_file = sum(issues_per_file) / len(issues_per_file) if issues_per_file else 0
    max_issues_per_file = max(issues_per_file) if issues_per_file else 0
    
    return {
        "summary": {
            "total_files": total_files,
            "total_issues": total_issues,
            "avg_issues_per_file": avg_issues_per_file,
            "max_issues_per_file": max_issues_per_file
        },
        "rule_distribution": dict(rule_counts.most_common(10)),
        "severity_distribution": dict(severity_counts),
        "top_problematic_files": sorted(
            [(path, len(issues)) for path, issues in issues_data.items()],
            key=lambda x: x[1],
            reverse=True
        )[:5]
    }


def main():
    """主函数"""
    print("📊 Agent监控和统计工具")
    print("=" * 40)
    
    # 分析修复日志
    monitor = AgentMonitor()
    print("分析修复日志...")
    print(monitor.generate_report())
    
    # 保存报告
    monitor.save_report()
    
    # 分析原始issues
    print("\n" + "=" * 40)
    print("📋 原始Issues分析:")
    
    issues_analysis = analyze_issues_file()
    if "error" not in issues_analysis:
        summary = issues_analysis["summary"]
        print(f"📁 总文件数: {summary['total_files']}")
        print(f"🐛 总问题数: {summary['total_issues']}")
        print(f"📈 平均每文件问题数: {summary['avg_issues_per_file']:.2f}")
        print(f"📊 单文件最多问题数: {summary['max_issues_per_file']}")
        
        print("\n🎯 Top 5 规则:")
        for rule, count in list(issues_analysis["rule_distribution"].items())[:5]:
            print(f"  - {rule}: {count}")
        
        print("\n📄 问题最多的文件:")
        for file_path, count in issues_analysis["top_problematic_files"]:
            file_name = os.path.basename(file_path)
            print(f"  - {file_name}: {count} issues")
    else:
        print(f"❌ {issues_analysis['error']}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "monitor":
        # 实时监控模式
        monitor = AgentMonitor()
        monitor.real_time_monitor()
    else:
        # 分析模式
        main()
