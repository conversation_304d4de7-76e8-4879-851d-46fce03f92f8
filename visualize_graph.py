"""
可视化LangGraph工作流程
"""

import os
from code_fix_agent import CodeFixAgent


def visualize_agent_graph():
    """可视化Agent的工作流程图"""
    print("=== 生成Agent工作流程图 ===")
    
    try:
        # 初始化Agent
        agent = CodeFixAgent()
        
        # 获取图的可视化
        graph_image = agent.graph.get_graph().draw_mermaid_png()
        
        # 保存图片
        with open("agent_workflow.png", "wb") as f:
            f.write(graph_image)
        
        print("✅ 工作流程图已保存为: agent_workflow.png")
        
        # 生成Mermaid代码
        mermaid_code = agent.graph.get_graph().draw_mermaid()
        
        with open("agent_workflow.mmd", "w", encoding='utf-8') as f:
            f.write(mermaid_code)
        
        print("✅ Mermaid代码已保存为: agent_workflow.mmd")
        print("\n可以在以下网站查看Mermaid图:")
        print("https://mermaid.live/")
        
        return mermaid_code
        
    except Exception as e:
        print(f"❌ 生成图片失败: {e}")
        print("请确保安装了必要的依赖:")
        print("pip install pygraphviz")
        return None


def print_graph_structure():
    """打印图的结构信息"""
    print("\n=== Agent图结构信息 ===")
    
    try:
        agent = CodeFixAgent()
        graph = agent.graph.get_graph()
        
        print("节点 (Nodes):")
        for node in graph.nodes():
            print(f"  - {node}")
        
        print("\n边 (Edges):")
        for edge in graph.edges():
            print(f"  - {edge[0]} -> {edge[1]}")
        
        print(f"\n总节点数: {len(list(graph.nodes()))}")
        print(f"总边数: {len(list(graph.edges()))}")
        
    except Exception as e:
        print(f"❌ 获取图结构失败: {e}")


def generate_workflow_documentation():
    """生成工作流程文档"""
    print("\n=== 生成工作流程文档 ===")
    
    workflow_doc = """
# 代码修复Agent工作流程

## 节点说明

### 1. analyze_issues (分析节点)
- **功能**: 分析输入的代码质量问题
- **输入**: 文件路径和Issues列表
- **输出**: 分析结果和工具调用请求
- **工具**: 可能调用文件读取和规则查询工具

### 2. tools (工具节点)
- **功能**: 执行具体的工具调用
- **工具类型**:
  - `read_source_file`: 读取源代码文件
  - `get_rule_details`: 获取规则详细信息
  - `write_source_file`: 写入修复后的代码

### 3. generate_fixes (修复生成节点)
- **功能**: 基于分析结果生成具体的修复方案
- **输入**: 代码内容、规则信息、问题描述
- **输出**: 结构化的修复建议列表

### 4. validate_fixes (验证节点)
- **功能**: 验证修复方案的正确性（反思链）
- **验证内容**:
  - 是否解决了原始问题
  - 是否符合规则要求
  - 是否保持代码逻辑正确性
  - 是否引入新问题

### 5. apply_fixes (应用节点)
- **功能**: 将验证通过的修复应用到文件
- **操作**: 修改源文件并记录日志

## 流程控制

### 条件边 (Conditional Edges)

1. **_should_use_tools**
   - 判断是否需要调用工具
   - 基于消息中是否包含工具调用

2. **_should_retry**
   - 判断是否需要重试修复
   - 基于验证结果和重试次数

## 状态管理

Agent使用`CodeFixState`来维护整个处理过程中的状态：

```python
class CodeFixState(TypedDict):
    file_path: str                    # 文件路径
    issues: List[Dict[str, Any]]      # Issues列表
    original_code: str                # 原始代码
    rule_details: Dict[str, Any]      # 规则详情
    fix_suggestions: List[Dict]       # 修复建议
    validation_result: Dict           # 验证结果
    messages: List                    # 消息历史
```

## 错误处理

- **工具调用失败**: 返回错误信息，继续处理
- **解析失败**: 记录错误，尝试重新生成
- **验证失败**: 最多重试2次，超过则停止
- **文件操作失败**: 记录错误，跳过当前文件

## 日志记录

所有修复操作都会记录到`autofix_argument.log`文件中，包含：
- 文件路径
- 修复详情
- 时间戳
"""
    
    with open("workflow_documentation.md", "w", encoding='utf-8') as f:
        f.write(workflow_doc)
    
    print("✅ 工作流程文档已保存为: workflow_documentation.md")


def main():
    """主函数"""
    print("LangGraph Agent可视化工具")
    print("=" * 50)
    
    # 检查环境变量
    if not os.getenv("OPENAI_API_KEY"):
        print("Warning: OPENAI_API_KEY not set, 某些功能可能无法使用")
    
    # 生成可视化图
    mermaid_code = visualize_agent_graph()
    
    # 打印图结构
    print_graph_structure()
    
    # 生成文档
    generate_workflow_documentation()
    
    # 显示Mermaid代码
    if mermaid_code:
        print("\n=== Mermaid代码 ===")
        print(mermaid_code)
    
    print("\n" + "=" * 50)
    print("可视化完成！生成的文件:")
    print("- agent_workflow.png (工作流程图)")
    print("- agent_workflow.mmd (Mermaid代码)")
    print("- workflow_documentation.md (详细文档)")


if __name__ == "__main__":
    main()
