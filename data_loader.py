import os
import requests
import json
from dotenv import load_dotenv
load_dotenv()


class ThemisClient:
    def __init__(self, base_url: str = None, token: str = None):
        """初始化Themis客户端，所有参数从环境变量获取"""
        self.base_url = base_url or os.getenv("SERVER_URL")
        self.token = token or os.getenv("SERVER_TOKEN")
        self.git_project_id = os.getenv("GIT_PROJECT_ID")
        self.branch = os.getenv("BRANCH")
        if not self.token:
            raise ValueError("SERVER_TOKEN environment variable is not set")
        if not self.git_project_id:
            raise ValueError("GIT_PROJECT_ID environment variable is not set")
        self.headers = {
            "Cookie": f"{self.token}",
            "Content-Type": "application/json"
        }
    
    def get_issues(self) -> None:
        """分页获取项目的所有 issues，并将指定字段写入 data/issues_original.json 文件，并根据 location 聚合写入 data/issues.json"""
        page = 1
        page_size = 500
        total = None
        all_issues = []
        output_path = os.path.join("data", "issues_original.json")
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        while True:
            payload = {
                "gitProjectId": self.git_project_id,
                "branch": self.branch,
                "severities": None,
                "authors": None,
                "p": page,
                "ps": page_size,
                "statuses": None,
                "types": "BUG,VULNERABILITY",
                "rules": None
            }
            url = f"{self.base_url}/api/quality/local/v2/platform/issue/list"
            try:
                response = requests.post(url, headers=self.headers, json=payload)
                if response.status_code != 200:
                    print(f"[ERROR] Failed to get issues: HTTP {response.status_code}, {response.text}")
                    break
                data = response.json()
                if data.get("status") != 200:
                    print(f"[ERROR] API error: {data.get('message')}")
                    break
                if total is None:
                    total = data.get("data", {}).get("total", 0)
                if total == 0:
                    break
                issues = data.get("data", {}).get("issues", [])
                # 只保留指定字段
                for issue in issues:
                    filtered = {
                        "rule": issue.get("rule"),
                        "location": issue.get("location"),
                        "line": issue.get("line"),
                        "textRange": issue.get("textRange"),
                        "message": issue.get("message")
                    }
                    all_issues.append(filtered)
                if page * page_size >= total:
                    break
                page += 1
            except Exception as e:
                print(f"[ERROR] Error getting issues: {str(e)}")
                break
        # 写入 issues_original.json
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(all_issues, f, ensure_ascii=False, indent=2)
        print(f"[INFO] 写入 {len(all_issues)} 条 issues 到 {output_path}")
        # 根据 location 聚合
        location_map = {}
        for issue in all_issues:
            loc = issue.get("location")
            if not loc:
                continue
            if loc not in location_map:
                location_map[loc] = []
            location_map[loc].append(issue)
        # 写入 issues.json
        issues_json_path = os.path.join("data", "issues.json")
        with open(issues_json_path, "w", encoding="utf-8") as f:
            json.dump(location_map, f, ensure_ascii=False, indent=2)
        print(f"[INFO] 按 location 聚合写入 {issues_json_path}")
    
    def get_rules(self, rule_keys: list) -> dict:
        """批量获取规则详情，并将所有规则的 title 和 htmlDesc 写入 data/rules_origin.json 文件"""
        rules = {}
        for rule_key in rule_keys:
            rule = self.get_rule(rule_key)
            if rule:
                rules[rule_key] = {
                    "title": rule.get("title", ""),
                    "htmlDesc": rule.get("htmlDesc", "")
                }
        # 写入文件
        output_path = os.path.join("data", "rules_origin.json")
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(rules, f, ensure_ascii=False, indent=2)
        print(f"[INFO] 已写入 {len(rules)} 条规则到 {output_path}")
        return rules

    def get_rule(self, rule_key: str) -> dict:
        """获取单个规则详情，返回包含 title 和 htmlDesc 的字典"""
        url = f"{self.base_url}/api/quality/v2/platform/rule/detail"
        payload = {
            "ruleKey": rule_key,
            "profileName": ""
        }
        try:
            response = requests.post(url, headers=self.headers, json=payload)
            if response.status_code != 200:
                print(f"[ERROR] Failed to get rule {rule_key}: HTTP {response.status_code}, {response.text}")
                return None
            data = response.json()
            if not data or data.get("status") != 200:
                print(f"[ERROR] API error for rule {rule_key}: {data.get('message', 'No message')}")
                return None
            rule = data.get("data", {})
            return {
                "title": rule.get("title", ""),
                "htmlDesc": rule.get("htmlDesc", "")
            }
        except Exception as e:
            print(f"[ERROR] Error getting rule {rule_key}: {str(e)}")
            return None

def load_data(force_fetch=False):
    """优先读取本地 issues.json，不存在则调接口获取。再提取 ruleKeys 调 get_rules。"""
    issue_file = "data/issues.json"
    rules_file = "data/rules_origin.json"
    # 1. 获取 issues
    if not os.path.exists(issue_file) or force_fetch:
        print("[INFO] 本地未找到 issues.json，调用接口获取...")
        client = ThemisClient()
        client.get_issues()
    else:
        print("[INFO] 读取本地 issues.json ...")
    with open(issue_file, "r", encoding="utf-8") as f:
        issues = json.load(f)
    if not issues:
        print("[ERROR] 未找到问题或获取问题失败")
        return [], {}
    print(f"[INFO] 找到 {len(issues)} 个 location 的问题聚合")
    # 2. 获取 ruleKeys 并获取 rules
    # 聚合后 issues 是 {location: [issue, ...]}，需遍历所有 issue
    rule_keys = set()
    for issue_list in issues.values():
        for issue in issue_list:
            if "rule" in issue:
                rule_keys.add(issue["rule"])
    if not os.path.exists(rules_file) or force_fetch:
        print("[INFO] 获取所有规则信息 ...")
        client = ThemisClient()
        client.get_rules(list(rule_keys))
    else:
        print("[INFO] 读取本地 rules_origin.json ...")
    with open(rules_file, "r", encoding="utf-8") as f:
        rules = json.load(f)
    if not rules:
        print("[ERROR] 未找到规则或获取规则失败")
        return issues, {}
    print(f"[INFO] 获取了 {len(rules)} 条规则")
    return issues, rules 


if __name__ == "__main__":
    load_data()