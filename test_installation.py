#!/usr/bin/env python3
"""
测试依赖安装是否成功
"""

def test_imports():
    """测试所有必要的导入"""
    print("🧪 测试依赖包导入...")
    
    try:
        # 测试核心依赖
        import langchain_core
        print("✅ langchain-core 导入成功")
        
        import langchain_openai
        print("✅ langchain-openai 导入成功")
        
        import langgraph
        print("✅ langgraph 导入成功")
        
        import langchain_community
        print("✅ langchain-community 导入成功")
        
        import pydantic
        print("✅ pydantic 导入成功")
        
        from typing_extensions import TypedDict
        print("✅ typing-extensions 导入成功")
        
        # 测试具体的类和函数
        from langchain_core.tools import tool
        from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
        from langchain_openai import ChatOpenAI
        from langgraph.graph import StateGraph, START, END, add_messages
        from langgraph.prebuilt import ToolNode
        print("✅ 所有核心类导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False


def test_agent_import():
    """测试Agent代码导入"""
    print("\n🤖 测试Agent代码导入...")
    
    try:
        from code_fix_agent import CodeFixAgent
        print("✅ CodeFixAgent 导入成功")
        
        # 测试工具导入
        from code_fix_agent import read_source_file, get_rule_details, write_source_file
        print("✅ 工具函数导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ Agent导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False


def test_data_files():
    """测试数据文件是否存在"""
    print("\n📁 测试数据文件...")
    
    import os
    
    files_to_check = [
        "data/issues.json",
        "data/rules_origin.json"
    ]
    
    all_exist = True
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {file_path} 存在")
        else:
            print(f"❌ {file_path} 不存在")
            all_exist = False
    
    return all_exist


def test_basic_functionality():
    """测试基本功能"""
    print("\n⚙️ 测试基本功能...")
    
    try:
        # 测试创建Agent实例（不需要API密钥）
        from code_fix_agent import CodeFixAgent
        
        # 这里不实际创建Agent，因为需要API密钥
        print("✅ Agent类定义正确")
        
        # 测试工具函数
        from code_fix_agent import read_source_file, get_rule_details
        
        # 测试读取规则文件
        rule_result = get_rule_details("squid:S2111")
        if "BigDecimal" in rule_result:
            print("✅ 规则查询功能正常")
        else:
            print("⚠️ 规则查询结果可能有问题")
        
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 代码质量Issue自动修复Agent - 安装测试")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        ("依赖包导入", test_imports),
        ("Agent代码导入", test_agent_import),
        ("数据文件检查", test_data_files),
        ("基本功能测试", test_basic_functionality)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！Agent已准备就绪。")
        print("\n下一步:")
        print("1. 设置 OPENAI_API_KEY 环境变量")
        print("2. 运行 python demo.py 查看演示")
        print("3. 运行 python main.py 处理实际文件")
    else:
        print("⚠️ 部分测试失败，请检查安装。")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
