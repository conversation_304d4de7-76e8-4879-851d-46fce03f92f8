#!/usr/bin/env python3
"""
无需API密钥的演示程序 - 展示Agent的工作流程和数据处理能力
"""

import json
import os
from collections import Counter


def load_issues_data():
    """加载issues数据"""
    try:
        with open('data/issues.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载issues数据失败: {e}")
        return {}


def load_rules_data():
    """加载规则数据"""
    try:
        with open('data/rules_origin.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载规则数据失败: {e}")
        return {}


def analyze_issues(issues_data):
    """分析issues数据"""
    print("📊 Issues数据分析:")
    print("=" * 40)
    
    total_files = len(issues_data)
    total_issues = sum(len(issues) for issues in issues_data.values())
    
    print(f"📁 总文件数: {total_files}")
    print(f"🐛 总问题数: {total_issues}")
    print(f"📈 平均每文件问题数: {total_issues/total_files:.2f}")
    
    # 统计规则分布
    rule_counts = Counter()
    for file_issues in issues_data.values():
        for issue in file_issues:
            rule_counts[issue.get('rule', 'unknown')] += 1
    
    print(f"\n🎯 Top 10 规则类型:")
    for rule, count in rule_counts.most_common(10):
        percentage = (count / total_issues) * 100
        print(f"  - {rule}: {count} ({percentage:.1f}%)")
    
    # 找出问题最多的文件
    file_issue_counts = [(path, len(issues)) for path, issues in issues_data.items()]
    file_issue_counts.sort(key=lambda x: x[1], reverse=True)
    
    print(f"\n📄 问题最多的5个文件:")
    for file_path, count in file_issue_counts[:5]:
        file_name = os.path.basename(file_path)
        print(f"  - {file_name}: {count} issues")
    
    return rule_counts, file_issue_counts


def demonstrate_rule_analysis(rules_data, rule_counts):
    """演示规则分析"""
    print(f"\n🔍 规则详细分析:")
    print("=" * 40)
    
    # 分析最常见的几个规则
    top_rules = rule_counts.most_common(5)
    
    for rule, count in top_rules:
        if rule in rules_data:
            rule_info = rules_data[rule]
            title = rule_info.get('title', '未知标题')
            print(f"\n📋 {rule} ({count}个问题)")
            print(f"   标题: {title}")
            
            # 提取HTML描述中的关键信息
            html_desc = rule_info.get('htmlDesc', '')
            if '不合规的代码示例' in html_desc:
                print(f"   ✅ 包含代码示例")
            if '合规的代码示例' in html_desc:
                print(f"   ✅ 包含修复示例")
        else:
            print(f"\n📋 {rule} ({count}个问题)")
            print(f"   ⚠️ 规则详情未找到")


def simulate_fix_generation(file_path, issues):
    """模拟修复方案生成"""
    print(f"\n🔧 模拟修复: {os.path.basename(file_path)}")
    print("-" * 30)
    
    fixes = []
    for i, issue in enumerate(issues[:3], 1):  # 只处理前3个问题作为演示
        rule = issue.get('rule', 'unknown')
        line = issue.get('line', 0)
        message = issue.get('message', 'No message')
        
        print(f"{i}. 规则: {rule}")
        print(f"   行号: {line}")
        print(f"   问题: {message}")
        
        # 根据规则类型模拟修复建议
        if 'BigDecimal' in rule or 'S2111' in rule:
            fix = {
                "line": line,
                "before": "new BigDecimal(doubleValue)",
                "after": "BigDecimal.valueOf(doubleValue)"
            }
        elif 'WMI_WRONG_MAP_ITERATOR' in rule:
            fix = {
                "line": line,
                "before": "for (String key : map.keySet()) { Value value = map.get(key);",
                "after": "for (Map.Entry<String, Value> entry : map.entrySet()) { String key = entry.getKey(); Value value = entry.getValue();"
            }
        elif 'LongVariableUseLowerCaseIChecker' in rule:
            fix = {
                "line": line,
                "before": "long value = 123l;",
                "after": "long value = 123L;"
            }
        else:
            fix = {
                "line": line,
                "before": "// 原始代码",
                "after": "// 修复后的代码"
            }
        
        fixes.append(fix)
        print(f"   修复: {fix['before']} -> {fix['after']}")
        print()
    
    return fixes


def demonstrate_workflow():
    """演示完整的工作流程"""
    print("🤖 代码质量Issue自动修复Agent - 工作流程演示")
    print("=" * 60)
    print("注意: 这是无API密钥的演示版本，展示数据处理和分析能力")
    print()
    
    # 1. 加载数据
    print("📂 步骤1: 加载数据文件...")
    issues_data = load_issues_data()
    rules_data = load_rules_data()
    
    if not issues_data:
        print("❌ 无法加载issues数据，演示结束")
        return
    
    print(f"✅ 成功加载 {len(issues_data)} 个文件的issues")
    print(f"✅ 成功加载 {len(rules_data)} 个规则定义")
    
    # 2. 分析数据
    print(f"\n📊 步骤2: 分析issues数据...")
    rule_counts, file_issue_counts = analyze_issues(issues_data)
    
    # 3. 规则分析
    print(f"\n🔍 步骤3: 分析规则详情...")
    demonstrate_rule_analysis(rules_data, rule_counts)
    
    # 4. 模拟修复过程
    print(f"\n🔧 步骤4: 模拟修复过程...")
    print("选择问题最多的文件进行演示:")
    
    # 选择前2个问题最多的文件进行演示
    for file_path, issue_count in file_issue_counts[:2]:
        if issue_count > 0:
            file_issues = issues_data[file_path]
            fixes = simulate_fix_generation(file_path, file_issues)
            
            # 模拟验证过程
            print(f"🔍 验证修复方案...")
            print(f"✅ 生成了 {len(fixes)} 个修复建议")
            print(f"✅ 验证通过，准备应用修复")
            print()
    
    # 5. 总结
    print("📋 步骤5: 生成总结报告...")
    print("=" * 40)
    print("🎯 演示完成！")
    print(f"📁 分析了 {len(issues_data)} 个文件")
    print(f"🐛 发现了 {sum(len(issues) for issues in issues_data.values())} 个问题")
    print(f"🔧 演示了多种类型的修复方案")
    print()
    print("💡 实际使用时需要:")
    print("1. 设置 OPENAI_API_KEY 环境变量")
    print("2. 运行 python main.py 进行真实修复")
    print("3. 查看 autofix_argument.log 了解修复详情")


def show_agent_capabilities():
    """展示Agent的能力"""
    print("\n🚀 Agent核心能力展示:")
    print("=" * 40)
    
    capabilities = [
        "🧠 智能代码分析 - 基于Claude Sonnet 3.7模型",
        "🔧 自动修复生成 - 生成具体的代码修改方案", 
        "🔍 反思链验证 - 二次确认修复方案的正确性",
        "📊 多规则支持 - 支持FindBugs、SonarQube等规则",
        "📝 详细日志记录 - 完整的修复过程追踪",
        "⚡ 批量处理 - 同时处理多个文件和问题",
        "🎯 精确定位 - 精确到行号的修复建议",
        "🔄 工作流管理 - 基于LangGraph的状态管理"
    ]
    
    for capability in capabilities:
        print(f"  {capability}")
    
    print(f"\n🛠️ 支持的规则类型:")
    rule_types = [
        "BigDecimal构造函数问题",
        "Map迭代器效率问题", 
        "Long字面量格式问题",
        "浮点数比较问题",
        "线程池创建问题",
        "日志参数拼接问题",
        "方法覆盖注解问题",
        "未使用字段问题"
    ]
    
    for rule_type in rule_types:
        print(f"  ✅ {rule_type}")


if __name__ == "__main__":
    try:
        demonstrate_workflow()
        show_agent_capabilities()
    except KeyboardInterrupt:
        print("\n\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
