#!/usr/bin/env python3
"""
快速测试Agent功能
处理一个简单的代码质量问题
"""

import os
import json

# 设置环境变量
os.environ['OPENAI_API_KEY'] = 'sk-6dHyTrJ63OWstgZebpdAZSOhWEeBKvXEjGBMA5oblHZdIyLf'
os.environ['OPENAI_BASE_URL'] = 'https://www.dmxapi.com/v1'

def create_test_file():
    """创建测试文件"""
    test_file = "test_quick.java"
    test_code = """public class TestQuick {
    public void testBigDecimal() {
        double value = 1.23;
        BigDecimal bd = new BigDecimal(value);  // 问题：应该使用valueOf
        System.out.println(bd);
    }
}"""
    
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_code)
    
    return test_file

def create_test_issue(file_path):
    """创建测试issue"""
    return {
        "rule": "squid:S2111",
        "location": file_path,
        "line": 4,
        "textRange": {
            "startLine": 4,
            "endLine": 4,
            "startOffset": 20,
            "endOffset": 45
        },
        "message": "Use \"BigDecimal.valueOf\" instead."
    }

def main():
    print("🚀 快速测试代码修复Agent")
    print("=" * 40)
    
    try:
        # 1. 创建测试文件
        print("📝 创建测试文件...")
        test_file = create_test_file()
        print(f"✅ 创建测试文件: {test_file}")
        
        # 2. 创建测试issue
        print("\n🐛 创建测试问题...")
        test_issue = create_test_issue(test_file)
        print(f"✅ 问题类型: {test_issue['rule']}")
        print(f"✅ 问题行号: {test_issue['line']}")
        print(f"✅ 问题描述: {test_issue['message']}")
        
        # 3. 初始化Agent
        print("\n🤖 初始化Agent...")
        from code_fix_agent import CodeFixAgent
        agent = CodeFixAgent()
        print("✅ Agent初始化成功")
        
        # 4. 处理问题
        print("\n🔧 开始修复...")
        result = agent.process_file_issues(test_file, [test_issue])
        
        # 5. 显示结果
        print("\n📊 修复结果:")
        if "error" in result:
            print(f"❌ 修复失败: {result['error']}")
        else:
            fix_suggestions = result.get("fix_suggestions", [])
            print(f"✅ 生成了 {len(fix_suggestions)} 个修复建议")
            
            for i, fix in enumerate(fix_suggestions, 1):
                print(f"\n{i}. 行 {fix.get('line', 'Unknown')}:")
                print(f"   修复前: {fix.get('before', 'N/A')}")
                print(f"   修复后: {fix.get('after', 'N/A')}")
            
            # 显示修复后的代码
            try:
                with open(test_file, 'r', encoding='utf-8') as f:
                    fixed_code = f.read()
                print(f"\n📄 修复后的代码:")
                print(fixed_code)
            except Exception as e:
                print(f"❌ 读取修复后代码失败: {e}")
        
        # 6. 清理
        print(f"\n🧹 清理测试文件...")
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"✅ 删除测试文件: {test_file}")
        
        print("\n🎉 快速测试完成!")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
