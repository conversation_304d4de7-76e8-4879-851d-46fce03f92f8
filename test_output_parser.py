#!/usr/bin/env python3
"""
测试OutputParser功能
"""

import os
import json

# 设置环境变量
os.environ['OPENAI_API_KEY'] = 'sk-6dHyTrJ63OWstgZebpdAZSOhWEeBKvXEjGBMA5oblHZdIyLf'
os.environ['OPENAI_BASE_URL'] = 'https://www.dmxapi.com/v1'

def test_output_parser():
    """测试OutputParser功能"""
    print("🧪 测试OutputParser功能")
    print("=" * 40)
    
    try:
        # 导入必要的类
        from code_fix_agent import CodeFixSuggestions, CodeFix
        from langchain_core.output_parsers import PydanticOutputParser
        
        # 创建OutputParser
        parser = PydanticOutputParser(pydantic_object=CodeFixSuggestions)
        
        print("✅ OutputParser创建成功")
        
        # 显示格式指令
        format_instructions = parser.get_format_instructions()
        print(f"\n📋 格式指令:")
        print(format_instructions)
        
        # 测试解析示例
        test_json = """
{
    "fixes": [
        {
            "line": 185,
            "before": "for (Object province : provinceMap.keySet()) {",
            "after": "for (Map.Entry<Object, Object> provinceEntry : provinceMap.entrySet()) {"
        }
    ],
    "summary": "将keySet迭代改为entrySet迭代以提高效率"
}
"""
        
        print(f"\n🔧 测试解析:")
        parsed = parser.parse(test_json)
        print(f"✅ 解析成功!")
        print(f"修复数量: {len(parsed.fixes)}")
        print(f"总结: {parsed.summary}")
        
        for i, fix in enumerate(parsed.fixes, 1):
            print(f"\n{i}. 行 {fix.line}:")
            print(f"   修复前: {fix.before}")
            print(f"   修复后: {fix.after}")
        
        return True
        
    except Exception as e:
        print(f"❌ OutputParser测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_agent_with_output_parser():
    """测试Agent使用OutputParser"""
    print(f"\n🤖 测试Agent使用OutputParser")
    print("=" * 40)
    
    try:
        # 加载示例问题
        with open('data/issues.json', 'r', encoding='utf-8') as f:
            issues_data = json.load(f)
        
        # 选择LocationEntity.java文件
        test_file = "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/LocationEntity.java"
        
        if test_file not in issues_data:
            print(f"❌ 测试文件 {test_file} 不存在")
            return False
        
        test_issues = issues_data[test_file]
        print(f"📄 测试文件: {test_file}")
        print(f"🐛 问题数量: {len(test_issues)}")
        
        # 初始化Agent
        from code_fix_agent import CodeFixAgent
        agent = CodeFixAgent()
        print("✅ Agent初始化成功")
        
        # 处理问题
        print(f"\n🔧 处理问题...")
        result = agent.process_file_issues(test_file, test_issues)
        
        if "error" in result:
            print(f"❌ 处理失败: {result['error']}")
            return False
        
        # 显示结果
        fix_suggestions = result.get("fix_suggestions", [])
        parsed_summary = result.get("parsed_summary", "")
        
        print(f"✅ 处理成功!")
        print(f"🔧 修复建议数量: {len(fix_suggestions)}")
        
        if parsed_summary:
            print(f"📝 AI总结: {parsed_summary}")
        
        if fix_suggestions:
            print(f"\n📋 修复详情:")
            for i, fix in enumerate(fix_suggestions, 1):
                line = fix.get('line', 'Unknown')
                before = fix.get('before', 'N/A')
                after = fix.get('after', 'N/A')
                print(f"\n{i}. 行 {line}:")
                print(f"   修复前: {before[:100]}{'...' if len(before) > 100 else ''}")
                print(f"   修复后: {after[:100]}{'...' if len(after) > 100 else ''}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_with_without_parser():
    """比较使用和不使用OutputParser的效果"""
    print(f"\n📊 比较OutputParser效果")
    print("=" * 40)
    
    print("✅ 使用OutputParser的优势:")
    print("1. 🎯 类型安全 - 确保输出符合预期结构")
    print("2. 🛡️ 错误处理 - 内置解析错误处理")
    print("3. 📝 格式指令 - 自动生成格式说明")
    print("4. ✅ 验证 - 自动验证字段类型和必需性")
    print("5. 🔄 一致性 - 标准化的输出格式")
    
    print(f"\n⚠️ 之前手动解析的问题:")
    print("1. 🐛 JSON格式错误难以处理")
    print("2. 🔍 需要手动提取和验证")
    print("3. 💥 容易出现解析异常")
    print("4. 🔧 维护成本高")
    
    return True

def main():
    """主测试函数"""
    print("🚀 OutputParser功能测试")
    print("=" * 50)
    
    tests = [
        ("OutputParser基础功能", test_output_parser),
        ("Agent使用OutputParser", test_agent_with_output_parser),
        ("效果对比", compare_with_without_parser)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print(f"\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！OutputParser工作正常。")
    else:
        print("⚠️ 部分测试失败。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
