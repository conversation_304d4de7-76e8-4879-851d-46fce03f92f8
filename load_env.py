#!/usr/bin/env python3
"""
环境变量加载器
自动从.env文件加载环境变量
"""

import os


def load_env_file(env_file: str = ".env"):
    """从.env文件加载环境变量"""
    if not os.path.exists(env_file):
        return False
    
    try:
        with open(env_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                
                # 跳过空行和注释
                if not line or line.startswith('#'):
                    continue
                
                # 解析键值对
                if '=' not in line:
                    continue
                
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip()
                
                # 移除引号
                if value.startswith('"') and value.endswith('"'):
                    value = value[1:-1]
                elif value.startswith("'") and value.endswith("'"):
                    value = value[1:-1]
                
                # 只设置非空值
                if value:
                    os.environ[key] = value
        
        return True
        
    except Exception as e:
        print(f"Warning: Failed to load {env_file}: {e}")
        return False


def check_required_env():
    """检查必需的环境变量"""
    required_vars = ["OPENAI_API_KEY"]
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    return missing_vars


def auto_load_env():
    """自动加载环境变量"""
    # 尝试加载.env文件
    if load_env_file():
        print("✅ 从 .env 文件加载环境变量")
    
    # 检查必需的环境变量
    missing = check_required_env()
    if missing:
        print(f"⚠️ 缺少必需的环境变量: {', '.join(missing)}")
        print("请运行 python setup_env.py 进行设置")
        return False
    
    return True


if __name__ == "__main__":
    auto_load_env()
