#!/usr/bin/env python3
"""
简化的Agent测试
"""

import os
import json

# 设置环境变量
os.environ['OPENAI_API_KEY'] = 'sk-6dHyTrJ63OWstgZebpdAZSOhWEeBKvXEjGBMA5oblHZdIyLf'
os.environ['OPENAI_BASE_URL'] = 'https://www.dmxapi.com/v1'

def main():
    print("🚀 简化Agent测试")
    print("=" * 30)
    
    try:
        # 1. 测试配置验证
        print("🔧 测试配置...")
        from config import validate_base_path, validate_data_files
        validate_base_path()
        validate_data_files()
        print("✅ 配置验证通过")
        
        # 2. 测试工具函数
        print("\n🛠️ 测试工具函数...")
        from code_fix_agent import read_source_file, get_rule_details
        
        # 测试规则查询
        rule_info = get_rule_details("squid:S2111")
        if "BigDecimal" in rule_info:
            print("✅ 规则查询正常")
        else:
            print("⚠️ 规则查询结果异常")
        
        # 3. 测试文件读取
        print("\n📄 测试文件读取...")
        with open('data/issues.json', 'r', encoding='utf-8') as f:
            issues_data = json.load(f)
        
        # 选择一个简单的文件
        sample_file = None
        sample_issue = None
        
        for file_path, issues in issues_data.items():
            if issues and len(issues) == 1:  # 只选择有单个问题的文件
                sample_file = file_path
                sample_issue = issues[0]
                break
        
        if not sample_file:
            # 如果没有单个问题的文件，就选择第一个文件的第一个问题
            sample_file = list(issues_data.keys())[0]
            sample_issue = issues_data[sample_file][0]
        
        print(f"📄 测试文件: {sample_file}")
        print(f"🐛 问题规则: {sample_issue.get('rule', 'Unknown')}")
        
        # 测试读取文件
        try:
            content = read_source_file(sample_file)
            print(f"✅ 文件读取成功，大小: {len(content)} 字符")
        except Exception as e:
            print(f"❌ 文件读取失败: {e}")
            return False
        
        # 4. 测试Agent初始化（不运行工作流）
        print("\n🤖 测试Agent初始化...")
        from code_fix_agent import CodeFixAgent
        agent = CodeFixAgent()
        print("✅ Agent初始化成功")
        
        print("\n🎉 所有基础测试通过！")
        print("Agent已准备就绪，可以处理代码质量问题。")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
