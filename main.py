"""
主程序：读取data/issues.json，将所有文件的issues交由agent处理
"""

import json
import os
import sys
from typing import Dict, List, Any
from code_fix_agent import CodeFixAgent


def load_issues(issues_file: str = "data/issues.json") -> Dict[str, List[Dict[str, Any]]]:
    """加载issues.json文件"""
    try:
        with open(issues_file, 'r', encoding='utf-8') as f:
            issues_data = json.load(f)
        return issues_data
    except Exception as e:
        print(f"Error loading issues file {issues_file}: {e}")
        return {}


def save_results(results: List[Dict[str, Any]], output_file: str = "fix_results.json"):
    """保存修复结果"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"Results saved to {output_file}")
    except Exception as e:
        print(f"Error saving results: {e}")


def main():
    """主函数"""
    print("=== 代码质量Issue自动修复Agent ===")
    print("基于LangGraph实现，支持反思链验证")
    print()
    
    # 检查环境变量
    if not os.getenv("OPENAI_API_KEY"):
        print("Error: OPENAI_API_KEY environment variable not set")
        print("Please set your OpenAI API key:")
        print("export OPENAI_API_KEY='your-api-key-here'")
        sys.exit(1)
    
    # 加载issues数据
    print("Loading issues from data/issues.json...")
    issues_data = load_issues()
    
    if not issues_data:
        print("No issues found or failed to load issues file")
        return
    
    print(f"Found {len(issues_data)} files with issues")
    
    # 初始化Agent
    print("Initializing CodeFixAgent...")
    agent = CodeFixAgent()
    
    # 处理每个文件的issues
    results = []
    total_files = len(issues_data)
    
    for i, (file_path, file_issues) in enumerate(issues_data.items(), 1):
        print(f"\n[{i}/{total_files}] Processing: {file_path}")
        print(f"Issues count: {len(file_issues)}")
        
        # 显示issues概要
        for j, issue in enumerate(file_issues, 1):
            rule = issue.get("rule", "Unknown")
            line = issue.get("line", "Unknown")
            message = issue.get("message", "No message")[:100]
            print(f"  {j}. {rule} (Line {line}): {message}...")
        
        try:
            # 处理文件issues
            result = agent.process_file_issues(file_path, file_issues)
            
            # 添加处理结果
            result_entry = {
                "file_path": file_path,
                "original_issues_count": len(file_issues),
                "issues": file_issues,
                "result": result,
                "status": "success" if "error" not in result else "error"
            }
            
            results.append(result_entry)
            
            if "error" in result:
                print(f"  ❌ Error: {result['error']}")
            else:
                fix_count = len(result.get("fix_suggestions", []))
                print(f"  ✅ Generated {fix_count} fixes")
                
        except Exception as e:
            print(f"  ❌ Exception: {e}")
            results.append({
                "file_path": file_path,
                "original_issues_count": len(file_issues),
                "issues": file_issues,
                "result": {"error": str(e)},
                "status": "exception"
            })
    
    # 保存结果
    print(f"\n=== Processing Complete ===")
    save_results(results)
    
    # 统计结果
    success_count = sum(1 for r in results if r["status"] == "success")
    error_count = sum(1 for r in results if r["status"] in ["error", "exception"])
    
    print(f"Successfully processed: {success_count}/{total_files} files")
    print(f"Failed: {error_count}/{total_files} files")
    
    # 显示修复统计
    total_fixes = sum(len(r["result"].get("fix_suggestions", [])) for r in results if r["status"] == "success")
    total_issues = sum(r["original_issues_count"] for r in results)
    
    print(f"Total issues: {total_issues}")
    print(f"Total fixes generated: {total_fixes}")
    
    if os.path.exists("autofix_argument.log"):
        print(f"Fix log saved to: autofix_argument.log")


def process_single_file(file_path: str, issues_file: str = "data/issues.json"):
    """处理单个文件的issues（用于测试）"""
    print(f"=== Processing single file: {file_path} ===")
    
    # 加载issues数据
    issues_data = load_issues(issues_file)
    
    if file_path not in issues_data:
        print(f"File {file_path} not found in issues data")
        return
    
    file_issues = issues_data[file_path]
    print(f"Found {len(file_issues)} issues for this file")
    
    # 初始化Agent
    agent = CodeFixAgent()
    
    # 处理文件issues
    result = agent.process_file_issues(file_path, file_issues)
    
    # 显示结果
    if "error" in result:
        print(f"Error: {result['error']}")
    else:
        fix_suggestions = result.get("fix_suggestions", [])
        print(f"Generated {len(fix_suggestions)} fixes:")
        
        for i, fix in enumerate(fix_suggestions, 1):
            print(f"\n{i}. Line {fix.get('line', 'Unknown')}:")
            print(f"   Before: {fix.get('before', 'N/A')}")
            print(f"   After:  {fix.get('after', 'N/A')}")
    
    return result


if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) > 1:
        # 处理单个文件
        file_path = sys.argv[1]
        process_single_file(file_path)
    else:
        # 处理所有文件
        main()
