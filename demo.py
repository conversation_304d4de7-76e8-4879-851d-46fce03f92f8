"""
代码修复Agent演示程序
展示如何使用Agent修复具体的代码质量问题
"""

import os
import json
from code_fix_agent import CodeFixAgent


def demo_bigdecimal_fix():
    """演示BigDecimal构造函数问题的修复"""
    print("=== 演示1: BigDecimal构造函数问题修复 ===")
    
    # 创建演示文件
    demo_file = "demo_files/BigDecimalDemo.java"
    os.makedirs("demo_files", exist_ok=True)
    
    # 有问题的代码
    problematic_code = """public class BigDecimalDemo {
    public void calculatePrice() {
        double price = 19.99;
        double tax = 0.08;
        
        // 问题：使用double构造BigDecimal
        BigDecimal priceDecimal = new BigDecimal(price);
        BigDecimal taxDecimal = new BigDecimal(tax);
        
        BigDecimal total = priceDecimal.multiply(taxDecimal.add(BigDecimal.ONE));
        System.out.println("Total: " + total);
    }
}"""
    
    with open(demo_file, 'w', encoding='utf-8') as f:
        f.write(problematic_code)
    
    print("原始代码 (有问题):")
    print(problematic_code)
    print()
    
    # 定义issues
    issues = [
        {
            "rule": "squid:S2111",
            "location": demo_file,
            "line": 6,
            "textRange": {"startLine": 6, "endLine": 6, "startOffset": 30, "endOffset": 50},
            "message": "Use \"BigDecimal.valueOf\" instead."
        },
        {
            "rule": "findbugs:DMI_BIGDECIMAL_CONSTRUCTED_FROM_DOUBLE",
            "location": demo_file,
            "line": 7,
            "textRange": {"startLine": 7, "endLine": 7, "startOffset": 29, "endOffset": 48},
            "message": "BigDecimal constructed from double"
        }
    ]
    
    # 使用Agent修复
    agent = CodeFixAgent()
    result = agent.process_file_issues(demo_file, issues)
    
    # 显示结果
    if "error" not in result:
        print("✅ 修复成功！")
        print("修复方案:")
        for i, fix in enumerate(result.get("fix_suggestions", []), 1):
            print(f"{i}. 行 {fix['line']}: {fix['before']} -> {fix['after']}")
        
        print("\n修复后的代码:")
        with open(demo_file, 'r', encoding='utf-8') as f:
            print(f.read())
    else:
        print(f"❌ 修复失败: {result['error']}")
    
    return result


def demo_map_iterator_fix():
    """演示Map迭代器效率问题的修复"""
    print("\n=== 演示2: Map迭代器效率问题修复 ===")
    
    demo_file = "demo_files/MapIteratorDemo.java"
    
    # 有问题的代码
    problematic_code = """public class MapIteratorDemo {
    public void processUserData(Map<String, UserInfo> userMap) {
        // 问题：使用keySet()迭代，效率低下
        for (String userId : userMap.keySet()) {
            UserInfo user = userMap.get(userId);
            System.out.println("User: " + userId + ", Info: " + user);
        }
    }
    
    public void processConfig(Map<String, String> configMap) {
        // 另一个类似问题
        for (String key : configMap.keySet()) {
            String value = configMap.get(key);
            processConfigItem(key, value);
        }
    }
    
    private void processConfigItem(String key, String value) {
        // 处理配置项
    }
}"""
    
    with open(demo_file, 'w', encoding='utf-8') as f:
        f.write(problematic_code)
    
    print("原始代码 (有问题):")
    print(problematic_code)
    print()
    
    # 定义issues
    issues = [
        {
            "rule": "findbugs:WMI_WRONG_MAP_ITERATOR",
            "location": demo_file,
            "line": 4,
            "textRange": {"startLine": 4, "endLine": 4, "startOffset": 8, "endOffset": 60},
            "message": "使用keySet迭代器而不是entrySet迭代器导致效率低下"
        },
        {
            "rule": "findbugs:WMI_WRONG_MAP_ITERATOR", 
            "location": demo_file,
            "line": 10,
            "textRange": {"startLine": 10, "endLine": 10, "startOffset": 8, "endOffset": 55},
            "message": "使用keySet迭代器而不是entrySet迭代器导致效率低下"
        }
    ]
    
    # 使用Agent修复
    agent = CodeFixAgent()
    result = agent.process_file_issues(demo_file, issues)
    
    # 显示结果
    if "error" not in result:
        print("✅ 修复成功！")
        print("修复方案:")
        for i, fix in enumerate(result.get("fix_suggestions", []), 1):
            print(f"{i}. 行 {fix['line']}: {fix['before']} -> {fix['after']}")
        
        print("\n修复后的代码:")
        with open(demo_file, 'r', encoding='utf-8') as f:
            print(f.read())
    else:
        print(f"❌ 修复失败: {result['error']}")
    
    return result


def demo_long_literal_fix():
    """演示long字面量问题的修复"""
    print("\n=== 演示3: Long字面量小写l问题修复 ===")
    
    demo_file = "demo_files/LongLiteralDemo.java"
    
    # 有问题的代码
    problematic_code = """public class LongLiteralDemo {
    private static final long TIMEOUT = 5000l;  // 问题：小写l
    private static final long MAX_SIZE = 1024l; // 问题：小写l
    
    public void processData() {
        long startTime = System.currentTimeMillis();
        long count = 100l;  // 问题：小写l
        
        for (int i = 0; i < count; i++) {
            // 处理数据
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        System.out.println("Duration: " + duration + "ms");
    }
}"""
    
    with open(demo_file, 'w', encoding='utf-8') as f:
        f.write(problematic_code)
    
    print("原始代码 (有问题):")
    print(problematic_code)
    print()
    
    # 定义issues
    issues = [
        {
            "rule": "squid:custom:LongVariableUseLowerCaseIChecker",
            "location": demo_file,
            "line": 2,
            "textRange": {"startLine": 2, "endLine": 2, "startOffset": 44, "endOffset": 49},
            "message": "禁止使用小写字母l为long或Long类型变量赋值"
        },
        {
            "rule": "squid:custom:LongVariableUseLowerCaseIChecker",
            "location": demo_file,
            "line": 3,
            "textRange": {"startLine": 3, "endLine": 3, "startOffset": 43, "endOffset": 48},
            "message": "禁止使用小写字母l为long或Long类型变量赋值"
        },
        {
            "rule": "squid:custom:LongVariableUseLowerCaseIChecker",
            "location": demo_file,
            "line": 7,
            "textRange": {"startLine": 7, "endLine": 7, "startOffset": 20, "endOffset": 24},
            "message": "禁止使用小写字母l为long或Long类型变量赋值"
        }
    ]
    
    # 使用Agent修复
    agent = CodeFixAgent()
    result = agent.process_file_issues(demo_file, issues)
    
    # 显示结果
    if "error" not in result:
        print("✅ 修复成功！")
        print("修复方案:")
        for i, fix in enumerate(result.get("fix_suggestions", []), 1):
            print(f"{i}. 行 {fix['line']}: {fix['before']} -> {fix['after']}")
        
        print("\n修复后的代码:")
        with open(demo_file, 'r', encoding='utf-8') as f:
            print(f.read())
    else:
        print(f"❌ 修复失败: {result['error']}")
    
    return result


def show_validation_process():
    """展示验证过程"""
    print("\n=== 验证过程演示 ===")
    print("Agent使用反思链来验证修复方案：")
    print("1. 生成初始修复方案")
    print("2. 验证修复是否解决原问题")
    print("3. 检查是否引入新问题")
    print("4. 验证代码逻辑正确性")
    print("5. 如果验证失败，重新生成（最多2次）")
    print("6. 验证通过后应用修复")


def main():
    """运行所有演示"""
    print("🤖 代码质量Issue自动修复Agent演示")
    print("=" * 60)
    
    # 检查环境变量
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ Error: OPENAI_API_KEY environment variable not set")
        print("请设置OpenAI API密钥:")
        print("export OPENAI_API_KEY='your-api-key-here'")
        return
    
    try:
        # 运行演示
        demo_bigdecimal_fix()
        demo_map_iterator_fix()
        demo_long_literal_fix()
        show_validation_process()
        
        print("\n" + "=" * 60)
        print("🎉 所有演示完成！")
        
        # 检查生成的文件
        if os.path.exists("autofix_argument.log"):
            print("📝 修复日志已保存到: autofix_argument.log")
        
        print("\n生成的演示文件:")
        for file in ["BigDecimalDemo.java", "MapIteratorDemo.java", "LongLiteralDemo.java"]:
            path = f"demo_files/{file}"
            if os.path.exists(path):
                print(f"  - {path}")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
