#!/usr/bin/env python3
"""
监控批量修复进度
"""

import time
import os
import json
from datetime import datetime

def monitor_progress():
    """监控批量修复进度"""
    log_file = "batch_fix_v2.log"
    
    print("🔍 监控批量修复进度...")
    print("=" * 50)
    
    # 获取总文件数
    try:
        with open('data/issues.json', 'r', encoding='utf-8') as f:
            issues_data = json.load(f)
        total_files = len(issues_data)
        print(f"📊 总文件数: {total_files}")
    except Exception as e:
        print(f"❌ 无法读取issues数据: {e}")
        total_files = 82  # 默认值
    
    last_size = 0
    processed_files = 0
    start_time = datetime.now()
    
    while True:
        try:
            if os.path.exists(log_file):
                # 检查文件大小变化
                current_size = os.path.getsize(log_file)
                
                if current_size > last_size:
                    # 文件有更新，读取新内容
                    with open(log_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 统计处理的文件数
                    processed_files = content.count("Processing:")
                    generated_fixes = content.count("Generated") + content.count("Applied fix")
                    
                    # 计算进度
                    progress = (processed_files / total_files) * 100 if total_files > 0 else 0
                    elapsed = datetime.now() - start_time
                    
                    # 显示进度
                    print(f"\r🚀 进度: {processed_files}/{total_files} ({progress:.1f}%) | "
                          f"修复数: {generated_fixes} | "
                          f"耗时: {elapsed.total_seconds():.0f}s", end="", flush=True)
                    
                    last_size = current_size
                
                # 检查是否完成
                if processed_files >= total_files:
                    print(f"\n🎉 批量修复完成！")
                    break
                    
            else:
                print(f"\r⏳ 等待日志文件创建...", end="", flush=True)
            
            time.sleep(2)  # 每2秒检查一次
            
        except KeyboardInterrupt:
            print(f"\n👋 监控被用户中断")
            break
        except Exception as e:
            print(f"\n❌ 监控错误: {e}")
            break
    
    # 显示最终统计
    if os.path.exists(log_file):
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            final_processed = content.count("Processing:")
            final_fixes = content.count("Generated")
            errors = content.count("Error") + content.count("Warning")
            
            print(f"\n📊 最终统计:")
            print(f"  处理文件: {final_processed}/{total_files}")
            print(f"  生成修复: {final_fixes}")
            print(f"  警告/错误: {errors}")
            
            # 显示最后几行
            lines = content.strip().split('\n')
            if lines:
                print(f"\n📝 最后几行:")
                for line in lines[-5:]:
                    if line.strip():
                        print(f"  {line}")
                        
        except Exception as e:
            print(f"❌ 读取最终统计失败: {e}")

if __name__ == "__main__":
    monitor_progress()
