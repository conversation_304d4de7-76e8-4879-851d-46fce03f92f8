# Autofix Argument Log
# 记录代码质量问题修复操作

## 修复进度跟踪
开始时间: $(date)

## 待处理文件列表
1. ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java
2. ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java
3. ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SqlRuleUtils.java
4. ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/Department.java
5. ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/PotentialFalsePositives.java
6. ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/mr/rule/impl/MrPomSnapshotDependencyRule.java
7. ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/mybatis/MybatisPlusGenerator.java
8. ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/utils/IssueUtils.java
9. ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/datasource/ThemisDataSourceMapSetter.java
10. ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/sonar/mode/ScanModeService.java
... (更多文件)

## 修复记录
{"file": "demo_files/BigDecimalDemo.java", "fixes": [{"line": 6, "issue": "squid:S2111", "message": "Use \"BigDecimal.valueOf\" instead.", "fix": "Replace 'new BigDecimal(price)' with 'BigDecimal.valueOf(price)'.", "codeChange": "BigDecimal priceDecimal = BigDecimal.valueOf(price);"}, {"line": 7, "issue": "findbugs:DMI_BIGDECIMAL_CONSTRUCTED_FROM_DOUBLE", "message": "BigDecimal constructed from double", "fix": "Replace 'new BigDecimal(tax)' with 'BigDecimal.valueOf(tax)'.", "codeChange": "BigDecimal taxDecimal = BigDecimal.valueOf(tax);"}], "timestamp": "2025-06-02T18:50:43.581979"}
{"file": "test_quick.java", "fixes": [{"filePath": "test_quick.java", "line": 4, "issue": "squid:S2111", "message": "Use \"BigDecimal.valueOf\" instead.", "fix": {"description": "Replace the constructor call 'new BigDecimal(value)' with 'BigDecimal.valueOf(value)' to avoid precision issues.", "updatedCode": "BigDecimal bd = BigDecimal.valueOf(value);"}}], "timestamp": "2025-06-02T18:53:23.092000"}
{"file": "test_quick.java", "fixes": [{"filePath": "test_quick.java", "line": 4, "issue": "squid:S2111", "description": "Replace the usage of 'new BigDecimal(value)' with 'BigDecimal.valueOf(value)' to avoid precision issues caused by floating-point representation.", "fix": {"oldCode": "BigDecimal bd = new BigDecimal(value);", "newCode": "BigDecimal bd = BigDecimal.valueOf(value);"}}], "timestamp": "2025-06-02T18:53:50.506945"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/LocationEntity.java", "fixes": [{"issue": "findbugs:WMI_WRONG_MAP_ITERATOR", "location": {"filePath": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/LocationEntity.java", "line": 185}, "fix": {"description": "Replace the inefficient use of keySet iterator with entrySet iterator in the static initializer block of ProvinceCityMapping.", "codeChange": {"before": "for (Object province : provinceMap.keySet()) {\n    countyCityMap.put(province.toString(), province.toString());\n\n    HashMap cityMap = (HashMap) (provinceMap.get(province));\n    for (Object city : cityMap.keySet()) {\n        countyCityMap.put(city.toString(), city.toString());\n        cityProvinceMap.put(city.toString(), province.toString());\n        ArrayList countyList = (ArrayList) cityMap.get(city);\n        for (Object county : countyList) {\n            cityProvinceMap.put(county.toString(), province.toString());\n            countyCityMap.put(county.toString(), city.toString());\n        }\n    }\n}", "after": "for (Map.Entry<Object, Object> provinceEntry : provinceMap.entrySet()) {\n    Object province = provinceEntry.getKey();\n    countyCityMap.put(province.toString(), province.toString());\n\n    HashMap cityMap = (HashMap) provinceEntry.getValue();\n    for (Map.Entry<Object, Object> cityEntry : cityMap.entrySet()) {\n        Object city = cityEntry.getKey();\n        countyCityMap.put(city.toString(), city.toString());\n        cityProvinceMap.put(city.toString(), province.toString());\n        ArrayList countyList = (ArrayList) cityEntry.getValue();\n        for (Object county : countyList) {\n            cityProvinceMap.put(county.toString(), province.toString());\n            countyCityMap.put(county.toString(), city.toString());\n        }\n    }\n}"}}}], "timestamp": "2025-06-02T19:10:55.971653"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/LocationEntity.java", "fixes": [{"issue": "findbugs:WMI_WRONG_MAP_ITERATOR", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/LocationEntity.java", "line": 185, "description": "在静态初始化块中，使用了 keySet 的迭代器来访问 Map 的键和值，这种方式效率较低，建议改为使用 entrySet 的迭代器。", "fix": {"before": "for (Object province : provinceMap.keySet()) {\n    countyCityMap.put(province.toString(), province.toString());\n\n    HashMap cityMap = (HashMap) (provinceMap.get(province));\n    for (Object city : cityMap.keySet()) {\n        countyCityMap.put(city.toString(), city.toString());\n        cityProvinceMap.put(city.toString(), province.toString());\n        ArrayList countyList = (ArrayList) cityMap.get(city);\n        for (Object county : countyList) {\n            cityProvinceMap.put(county.toString(), province.toString());\n            countyCityMap.put(county.toString(), city.toString());\n        }\n    }\n}", "after": "for (Map.Entry<Object, Object> provinceEntry : provinceMap.entrySet()) {\n    Object province = provinceEntry.getKey();\n    HashMap cityMap = (HashMap) provinceEntry.getValue();\n\n    countyCityMap.put(province.toString(), province.toString());\n\n    for (Map.Entry<Object, Object> cityEntry : cityMap.entrySet()) {\n        Object city = cityEntry.getKey();\n        ArrayList countyList = (ArrayList) cityEntry.getValue();\n\n        countyCityMap.put(city.toString(), city.toString());\n        cityProvinceMap.put(city.toString(), province.toString());\n\n        for (Object county : countyList) {\n            cityProvinceMap.put(county.toString(), province.toString());\n            countyCityMap.put(county.toString(), city.toString());\n        }\n    }\n}"}}], "timestamp": "2025-06-02T19:11:37.713360"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/LocationEntity.java", "fixes": [{"line": 185, "before": "for (Object province : provinceMap.keySet()) {", "after": "for (Map.Entry<Object, Object> provinceEntry : provinceMap.entrySet()) {"}, {"line": 189, "before": "for (Object city : cityMap.keySet()) {", "after": "for (Map.Entry<Object, Object> cityEntry : cityMap.entrySet()) {"}, {"line": 190, "before": "countyCityMap.put(city.toString(), city.toString());", "after": "countyCityMap.put(cityEntry.getKey().toString(), cityEntry.getKey().toString());"}, {"line": 191, "before": "cityProvinceMap.put(city.toString(), province.toString());", "after": "cityProvinceMap.put(cityEntry.getKey().toString(), provinceEntry.getKey().toString());"}, {"line": 192, "before": "ArrayList countyList = (ArrayList) cityMap.get(city);", "after": "ArrayList countyList = (ArrayList) cityEntry.getValue();"}], "timestamp": "2025-06-02T19:15:23.075974"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/LocationEntity.java", "fixes": [{"line": 185, "before": "for (Object province : provinceMap.keySet()) {", "after": "for (Map.Entry<Object, Object> provinceEntry : provinceMap.entrySet()) {"}, {"line": 188, "before": "for (Object city : cityMap.keySet()) {", "after": "for (Map.Entry<Object, Object> cityEntry : cityMap.entrySet()) {"}, {"line": 189, "before": "ArrayList countyList = (ArrayList) cityMap.get(city);", "after": "ArrayList countyList = (ArrayList) cityEntry.getValue();"}], "timestamp": "2025-06-02T19:16:34.675346"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "fixes": [{"line": 247, "before": "BigDecimal bigDecimal = new BigDecimal(a);", "after": "BigDecimal bigDecimal = BigDecimal.valueOf(a);"}, {"line": 248, "before": "BigDecimal bigDecimal2 = new BigDecimal(2.4f);", "after": "BigDecimal bigDecimal2 = BigDecimal.valueOf(2.4f);"}, {"line": 249, "before": "BigDecimal bigDecimal3 = new BigDecimal(3.8);", "after": "BigDecimal bigDecimal3 = BigDecimal.valueOf(3.8);"}, {"line": 251, "before": "BigDecimal bigDecimal5 = new BigDecimal(a + b);", "after": "BigDecimal bigDecimal5 = BigDecimal.valueOf(a).add(BigDecimal.valueOf(b));"}, {"line": 255, "before": "return a.equals(b);", "after": "return a.compareTo(b) == 0;"}, {"line": 259, "before": "if (a.equals(b)) {", "after": "if (a.compareTo(b) == 0) {"}, {"line": 260, "before": "if (a.equals(BigDecimal.ZERO)) {", "after": "if (a.compareTo(BigDecimal.ZERO) == 0) {"}, {"line": 263, "before": "if (!BigDecimal.ZERO.equals(b)) {", "after": "if (BigDecimal.ZERO.compareTo(b) != 0) {"}, {"line": 266, "before": "boolean c = a.equals(BigDecimal.ZERO);", "after": "boolean c = a.compareTo(BigDecimal.ZERO) == 0;"}, {"line": 269, "before": "boolean d = BigDecimal.ZERO.equals(b);", "after": "boolean d = BigDecimal.ZERO.compareTo(b) == 0;"}, {"line": 270, "before": "boolean d = BigDecimal.ZERO.equals(b);", "after": "boolean d = BigDecimal.ZERO.compareTo(b) == 0;"}, {"line": 275, "before": "private final ThreadLocal<Object> kconfParameter = ThreadLocal.withInitial(Person::new);", "after": "private static final ThreadLocal<Object> kconfParameter = ThreadLocal.withInitial(Person::new);"}, {"line": 276, "before": "private final ThreadLocal<Object> redisData = ThreadLocal.withInitial(Person::new);", "after": "private static final ThreadLocal<Object> redisData = ThreadLocal.withInitial(Person::new);"}, {"line": 284, "before": "private final ThreadLocal<Map<String, Object>> itemLabelScoreMap = ThreadLocal.withInitial(() -> new HashMap<String, Object>() {{ put(\"adjustWatchtimeScore\", new Object()); put(\"formulaWatchtimeScore\", new Object()); put(\"clickScore\", new Object()); put(\"clickWatchtimeScore\", new Object()); put(\"pwatchtimeRankScore\", new Object()); }});", "after": "private static final ThreadLocal<Map<String, Object>> itemLabelScoreMap = ThreadLocal.withInitial(() -> new HashMap<String, Object>() {{ put(\"adjustWatchtimeScore\", new Object()); put(\"formulaWatchtimeScore\", new Object()); put(\"clickScore\", new Object()); put(\"clickWatchtimeScore\", new Object()); put(\"pwatchtimeRankScore\", new Object()); }});"}, {"line": 93, "before": "String[] a1 = (String[]) listOfString.toArray();", "after": "String[] a1 = listOfString.toArray(new String[0]);"}, {"line": 107, "before": "String[] a1 = (String[]) toArray();", "after": "String[] a1 = toArray(new String[0]);"}, {"line": 113, "before": "private long f3 = 3l;", "after": "private long f3 = 3L;"}, {"line": 116, "before": "f1 = 123l;", "after": "f1 = 123L;"}, {"line": 120, "before": "long b = 456l;", "after": "long b = 456L;"}, {"line": 122, "before": "Long e = 456l;", "after": "Long e = 456L;"}, {"line": 126, "before": "g = 111l;", "after": "g = 111L;"}, {"line": 127, "before": "H = 222l;", "after": "H = 222L;"}, {"line": 142, "before": "catch (Exception e) {", "after": "catch (Exception e) { // Exception ignored intentionally due to specific business logic"}, {"line": 167, "before": "if (triggerTime >= MoreObjects.firstNonNull(Long.valueOf(oldRights.getTriggerCron()), 0L)) {", "after": "if (oldRights != null && triggerTime >= MoreObjects.firstNonNull(Long.valueOf(oldRights.getTriggerCron()), 0L)) {"}, {"line": 37, "before": "try { lock.lock();", "after": "lock.lock(); try {"}, {"line": 40, "before": "try { lock.lock();", "after": "lock.lock(); try {"}, {"line": 42, "before": "try { lock.lock();", "after": "lock.lock(); try {"}, {"line": 55, "before": "try { lock.lock();", "after": "lock.lock(); try {"}, {"line": 51, "before": "try { lock.lock();", "after": "lock.lock(); try {"}, {"line": 65, "before": "try { lock.lock();", "after": "lock.lock(); try {"}, {"line": 61, "before": "try { lock.lock();", "after": "lock.lock(); try {"}, {"line": 69, "before": "try { lock.lock();", "after": "lock.lock(); try {"}, {"line": 28, "before": "public boolean equals(Object obj) {", "after": "public boolean equals(Object obj) { if (!(obj instanceof SubClassTest)) return false; return super.equals(obj); }"}], "timestamp": "2025-06-02T19:17:25.370789"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java", "fixes": [{"line": 49, "before": "private static String location = \"https://abc.shkwai.com/dfkal/we/4fg;s/fsldf/fls/sss.svg\"; // Noncompliant", "after": "private static String location = System.getenv(\"CDN_LOCATION_URL\"); // Compliant"}, {"line": 50, "before": "private static String domain = \"play-safe.app\"; // Noncompliant", "after": "private static String domain = System.getenv(\"CDN_DOMAIN\"); // Compliant"}], "timestamp": "2025-06-02T19:18:49.820135"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java", "fixes": [{"line": 49, "before": "private static String domain = \"play-safe.app\"; // Noncompliant", "after": "private static String domain = System.getenv(\"CDN_DOMAIN_URL\"); // Compliant"}], "timestamp": "2025-06-02T19:19:17.385830"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java", "fixes": [{"line": 49, "before": "private static String domain = \"play-safe.app\"; // Noncompliant", "after": "private static String domain = System.getenv(\"CDN_DOMAIN_URL\"); // Compliant"}], "timestamp": "2025-06-02T19:19:48.725914"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/Department.java", "fixes": [{"line": 25, "before": "@EqualsAndHashCode(callSuper = false)", "after": "@EqualsAndHashCode(callSuper = true)"}, {"line": 31, "before": "@EqualsAndHashCode", "after": "@EqualsAndHashCode(callSuper = true)"}], "timestamp": "2025-06-02T19:20:04.270218"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/Department.java", "fixes": [{"line": 25, "before": "@EqualsAndHashCode(callSuper = false)", "after": "@EqualsAndHashCode(callSuper = true)"}, {"line": 31, "before": "@EqualsAndHashCode", "after": "@EqualsAndHashCode(callSuper = true)"}], "timestamp": "2025-06-02T19:20:45.409627"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SqlRuleUtils.java", "fixes": [{"line": 31, "before": "this.lowerBound = lowerBound;", "after": "this.lowerBound = values.length > 0 ? Double.parseDouble(values[0]) : 0.0;"}, {"line": 32, "before": "this.upperBound = upperBound;", "after": "this.upperBound = values.length > 1 ? Double.parseDouble(values[1]) : 0.0;"}, {"line": 33, "before": "this.lowerInclusive = lowerInclusive;", "after": "this.lowerInclusive = false;"}, {"line": 34, "before": "this.upperInclusive = upperInclusive;", "after": "this.upperInclusive = false;"}, {"line": 69, "before": "this.lowerBound = lowerBound;", "after": "this.lowerBound = values.length > 0 ? Double.parseDouble(values[0]) : 0.0;"}, {"line": 70, "before": "this.upperBound = upperBound;", "after": "this.upperBound = values.length > 1 ? Double.parseDouble(values[1]) : 0.0;"}, {"line": 71, "before": "this.lowerInclusive = lowerInclusive;", "after": "this.lowerInclusive = false;"}, {"line": 72, "before": "this.upperInclusive = upperInclusive;", "after": "this.upperInclusive = false;"}, {"line": 6, "before": "private String[] values;", "after": "// Removed unused field 'values' as it is not utilized in the class\n// private String[] values;"}, {"line": 6, "before": "private boolean lowerInclusive;", "after": "// Removed unused field 'lowerInclusive' as it is not utilized in the class\n// private boolean lowerInclusive;"}, {"line": 6, "before": "private boolean upperInclusive;", "after": "// Removed unused field 'upperInclusive' as it is not utilized in the class\n// private boolean upperInclusive;"}], "timestamp": "2025-06-02T19:21:03.896136"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/LocationEntity.java", "fixes": [{"line": 185, "before": "for (Object province : provinceMap.keySet()) {", "after": "for (Map.Entry<Object, Object> provinceEntry : provinceMap.entrySet()) {"}, {"line": 188, "before": "HashMap cityMap = (HashMap) (provinceMap.get(province));", "after": "HashMap cityMap = (HashMap) (provinceEntry.getValue());"}], "timestamp": "2025-06-02T19:22:33.333093"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/LocationEntity.java", "fixes": [{"line": 185, "before": "for (Object province : provinceMap.keySet()) {", "after": "for (Map.Entry<Object, Object> provinceEntry : provinceMap.entrySet()) {"}, {"line": 188, "before": "HashMap cityMap = (HashMap) (provinceMap.get(province));", "after": "HashMap cityMap = (HashMap) (provinceEntry.getValue());"}], "timestamp": "2025-06-02T19:24:34.178249"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/LocationEntity.java", "fixes": [{"line": 185, "before": "for (Object province : provinceMap.keySet()) {", "after": "for (Map.Entry<Object, Object> provinceEntry : provinceMap.entrySet()) {"}], "timestamp": "2025-06-02T19:28:13.920865"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/LocationEntity.java", "fixes": [{"line": 185, "before": "for (Object province : provinceMap.keySet()) {", "after": "for (Map.Entry<Object, Object> provinceEntry : provinceMap.entrySet()) {"}, {"line": 186, "before": "countyCityMap.put(province.toString(), province.toString());", "after": "Object province = provinceEntry.getKey();\nObject provinceValue = provinceEntry.getValue();\ncountyCityMap.put(province.toString(), province.toString());"}, {"line": 188, "before": "HashMap cityMap = (HashMap) (provinceMap.get(province));", "after": "HashMap cityMap = (HashMap) provinceValue;"}, {"line": 189, "before": "for (Object city : cityMap.keySet()) {", "after": "for (Map.Entry<Object, Object> cityEntry : cityMap.entrySet()) {"}, {"line": 190, "before": "countyCityMap.put(city.toString(), city.toString());", "after": "Object city = cityEntry.getKey();\nObject cityValue = cityEntry.getValue();\ncountyCityMap.put(city.toString(), city.toString());"}, {"line": 192, "before": "ArrayList countyList = (ArrayList) cityMap.get(city);", "after": "ArrayList countyList = (ArrayList) cityValue;"}, {"line": 193, "before": "for (Object county : countyList) {", "after": "for (Object county : countyList) {"}], "timestamp": "2025-06-02T19:29:19.821999"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java", "fixes": [{"line": 49, "before": "String cdnDomain = \"https://kwai.com/resource\";", "after": "String cdnDomain = System.getenv(\"CDN_DOMAIN\");"}], "timestamp": "2025-06-02T19:30:41.246504"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "fixes": [{"line": 247, "before": "BigDecimal bigDecimal = new BigDecimal(a);", "after": "BigDecimal bigDecimal = BigDecimal.valueOf(a);"}, {"line": 248, "before": "BigDecimal bigDecimal2 = new BigDecimal(2.4f);", "after": "BigDecimal bigDecimal2 = BigDecimal.valueOf(2.4f);"}, {"line": 249, "before": "BigDecimal bigDecimal3 = new BigDecimal(3.8);", "after": "BigDecimal bigDecimal3 = BigDecimal.valueOf(3.8);"}, {"line": 251, "before": "BigDecimal bigDecimal5 = new BigDecimal(a + b);", "after": "BigDecimal bigDecimal5 = BigDecimal.valueOf(a).add(BigDecimal.valueOf(b));"}, {"line": 255, "before": "return a.equals(b);", "after": "return a.compareTo(b) == 0;"}, {"line": 259, "before": "if (a.equals(b)) {", "after": "if (a.compareTo(b) == 0) {"}, {"line": 260, "before": "if (a.equals(BigDecimal.ZERO)) {", "after": "if (a.compareTo(BigDecimal.ZERO) == 0) {"}, {"line": 263, "before": "if (!BigDecimal.ZERO.equals(b)) {", "after": "if (BigDecimal.ZERO.compareTo(b) != 0) {"}, {"line": 266, "before": "boolean c = a.equals(BigDecimal.ZERO);", "after": "boolean c = a.compareTo(BigDecimal.ZERO) == 0;"}, {"line": 269, "before": "boolean d = BigDecimal.ZERO.equals(b);", "after": "boolean d = BigDecimal.ZERO.compareTo(b) == 0;"}, {"line": 275, "before": "private final ThreadLocal<Object> kconfParameter = ThreadLocal.withInitial(Person::new);", "after": "private static final ThreadLocal<Object> kconfParameter = ThreadLocal.withInitial(Person::new);"}, {"line": 276, "before": "private final ThreadLocal<Object> redisData = ThreadLocal.withInitial(Person::new);", "after": "private static final ThreadLocal<Object> redisData = ThreadLocal.withInitial(Person::new);"}, {"line": 284, "before": "private final ThreadLocal<Map<String, Object>> itemLabelScoreMap = ThreadLocal.withInitial(() -> new HashMap<String, Object>() {{ put(\"adjustWatchtimeScore\", new Object()); put(\"formulaWatchtimeScore\", new Object()); put(\"clickScore\", new Object()); put(\"clickWatchtimeScore\", new Object()); put(\"pwatchtimeRankScore\", new Object()); }});", "after": "private static final ThreadLocal<Map<String, Object>> itemLabelScoreMap = ThreadLocal.withInitial(() -> new HashMap<String, Object>() {{ put(\"adjustWatchtimeScore\", new Object()); put(\"formulaWatchtimeScore\", new Object()); put(\"clickScore\", new Object()); put(\"clickWatchtimeScore\", new Object()); put(\"pwatchtimeRankScore\", new Object()); }});"}, {"line": 93, "before": "String[] a1 = (String[]) listOfString.toArray();", "after": "String[] a1 = listOfString.toArray(new String[0]);"}, {"line": 107, "before": "String[] a1 = (String[]) toArray();", "after": "String[] a1 = toArray(new String[0]);"}, {"line": 113, "before": "f3 = 3l;", "after": "f3 = 3L;"}, {"line": 116, "before": "f1 = 123l;", "after": "f1 = 123L;"}, {"line": 120, "before": "long b = 456l;", "after": "long b = 456L;"}, {"line": 122, "before": "Long e = 456l;", "after": "Long e = 456L;"}, {"line": 126, "before": "g = 111l;", "after": "g = 111L;"}, {"line": 127, "before": "H = 222l;", "after": "H = 222L;"}, {"line": 142, "before": "catch (Exception e) {", "after": "catch (Exception e) { // 捕获异常并忽略，原因：XXX"}, {"line": 37, "before": "try { lock.lock();", "after": "lock.lock(); try {"}, {"line": 40, "before": "try { lock.lock();", "after": "lock.lock(); try {"}, {"line": 42, "before": "try { lock.lock();", "after": "lock.lock(); try {"}, {"line": 55, "before": "try { lock.lock();", "after": "lock.lock(); try {"}, {"line": 51, "before": "try { lock.lock();", "after": "lock.lock(); try {"}, {"line": 65, "before": "try { lock.lock();", "after": "lock.lock(); try {"}, {"line": 61, "before": "try { lock.lock();", "after": "lock.lock(); try {"}, {"line": 69, "before": "try { lock.lock();", "after": "lock.lock(); try {"}], "timestamp": "2025-06-02T19:31:10.048394"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SqlRuleUtils.java", "fixes": [{"line": 31, "before": "this.lowerBound = lowerBound;", "after": "this.lowerBound = 0.0;"}, {"line": 32, "before": "this.upperBound = upperBound;", "after": "this.upperBound = 0.0;"}, {"line": 33, "before": "this.lowerInclusive = lowerInclusive;", "after": "this.lowerInclusive = false;"}, {"line": 34, "before": "this.upperInclusive = upperInclusive;", "after": "this.upperInclusive = false;"}, {"line": 69, "before": "this.lowerBound = lowerBound;", "after": "this.lowerBound = 0.0;"}, {"line": 70, "before": "this.upperBound = upperBound;", "after": "this.upperBound = 0.0;"}, {"line": 71, "before": "this.lowerInclusive = lowerInclusive;", "after": "this.lowerInclusive = false;"}, {"line": 72, "before": "this.upperInclusive = upperInclusive;", "after": "this.upperInclusive = false;"}, {"line": 31, "before": "this.lowerBound = lowerBound;", "after": "this.lowerBound = lowerBound;"}, {"line": 69, "before": "this.lowerBound = lowerBound;", "after": "this.lowerBound = lowerBound;"}, {"line": 1, "before": "private String[] values;", "after": ""}, {"line": 1, "before": "private boolean lowerInclusive;", "after": ""}, {"line": 1, "before": "private boolean upperInclusive;", "after": ""}], "timestamp": "2025-06-02T19:31:21.474608"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/Department.java", "fixes": [{"line": 25, "before": "@EqualsAndHashCode(callSuper = false)", "after": "@EqualsAndHashCode(callSuper = true)"}, {"line": 31, "before": "@EqualsAndHashCode(callSuper = false)", "after": "@EqualsAndHashCode(callSuper = true)"}], "timestamp": "2025-06-02T19:31:26.141202"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/PotentialFalsePositives.java", "fixes": [{"line": 90, "before": "input.trim();", "after": "input = input.trim();"}], "timestamp": "2025-06-02T19:31:41.345699"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/mr/rule/impl/MrPomSnapshotDependencyRule.java", "fixes": [{"line": 73, "before": "issue.setIssueId((long) Math.abs(issue.getIssueUniqId().hashCode()));", "after": "issue.setIssueId((long) (issue.getIssueUniqId().hashCode() & 0x7FFFFFFF));"}], "timestamp": "2025-06-02T19:31:47.654007"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/mybatis/MybatisPlusGenerator.java", "fixes": [{"line": 41, "before": ".setPassword(\"Kv11WIitxb7oPFgeyGKT4mXQ6a3CVNYw\")", "after": ".setPassword(System.getenv(\"DB_PASSWORD\"))"}, {"line": 40, "before": ".setUsername(\"ks_serveree_the_15596_v1_rw\")", "after": ".setUsername(System.getenv(\"DB_USERNAME\"))"}, {"line": 39, "before": ".setUrl(\"************************************************************************\")", "after": ".setUrl(System.getenv(\"DB_URL\"))"}, {"line": 38, "before": ".setDriverName(\"com.mysql.jdbc.Driver\")", "after": ".setDriverName(System.getenv(\"DB_DRIVER\"))"}, {"line": 37, "before": "mpg.setDataSource(new DataSourceConfig()", "after": "mpg.setDataSource(new DataSourceConfig()"}], "timestamp": "2025-06-02T19:31:55.447533"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/utils/IssueUtils.java", "fixes": [{"line": 527, "before": "Map<String, Long> map = Maps.newHashMap();", "after": "Map<String, Long> map = new HashMap<>();"}, {"line": 530, "before": "for (Map<String, Object> countMap : typeGroupList) {", "after": "for (Map<String, Object> countMap : typeGroupList) {"}, {"line": 530, "before": "String mapKey = \"\";", "after": "String mapKey = \"\";"}], "timestamp": "2025-06-02T19:32:02.875657"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/datasource/ThemisDataSourceMapSetter.java", "fixes": [{"line": 56, "before": "private static final String DATA_SOURCE_PASSWORD = \"password\";", "after": "private static final String DATA_SOURCE_PASSWORD = \"<REDACTED>\";"}, {"line": 103, "before": "dataSourceProps.put(DATA_SOURCE_PASSWORD, configMap.getOrDefault(dataSourceName, new HashMap<>()).get(DATA_SOURCE_PASSWORD));", "after": "dataSourceProps.put(DATA_SOURCE_PASSWORD, getSecurePassword(configMap, dataSourceName));"}, {"line": 104, "before": "", "after": "private static String getSecurePassword(HashMap<String, Map<String, String>> configMap, String dataSourceName) {\n    return configMap.getOrDefault(dataSourceName, new HashMap<>()).get(DATA_SOURCE_PASSWORD);\n}"}, {"line": 11, "before": "import org.apache.shardingsphere.infra.exception.ShardingSphereException;", "after": "import org.apache.shardingsphere.infra.exception.ShardingSphereException;\nimport java.util.Map.Entry;"}, {"line": 17, "before": "import org.apache.shardingsphere.spring.boot.util.PropertyUtil;", "after": "import org.apache.shardingsphere.spring.boot.util.PropertyUtil;\nimport java.util.Set;"}, {"line": 103, "before": "for (String each : getDataSourceNames(environment)) {", "after": "for (Entry<String, Object> entry : dataSourceProps.entrySet()) {"}, {"line": 104, "before": "result.put(each, getDataSource(environment, each));", "after": "String key = entry.getKey();\nObject value = entry.getValue();\nresult.put(key, getDataSource(environment, key));"}], "timestamp": "2025-06-02T19:32:13.883696"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/sonar/mode/ScanModeService.java", "fixes": [{"line": 63, "before": "Map<Long, PCheckBase> pCheckBaseMap = pCheckBases.stream().collect(Collectors.toMap(PCheckBase::getId, Function.identity()));", "after": "Map<Long, PCheckBase> pCheckBaseMap = pCheckBases.stream().collect(Collectors.toMap(PCheckBase::getId, Function.identity(), (existing, replacement) -> existing));"}], "timestamp": "2025-06-02T19:32:19.458454"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java", "fixes": [{"line": 49, "before": "String cdnUrl = \"https://cdn.kwai.com/resource\";", "after": "String cdnUrl = System.getenv(\"CDN_URL\"); // 从环境变量中获取CDN域名"}], "timestamp": "2025-06-02T19:32:21.994103"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/common/mappers/CheckRuleMapper.java", "fixes": [{"line": 24, "before": "@Select(\"select rule_key from check_rule where deleted = 0 and can_skip = 0\")", "after": "@Select(\"select rule_key from check_rule where deleted = 0 and can_skip = 0 limit 100\")"}], "timestamp": "2025-06-02T19:32:24.919627"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/client/compile/CompileApi.java", "fixes": [{"line": 70, "before": "if (!response.isOk()) {", "after": "if (response == null || !response.isOk()) {"}, {"line": 97, "before": "if (!response.isOk()) {", "after": "if (response == null || !response.isOk()) {"}], "timestamp": "2025-06-02T19:32:30.037075"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/LocationEntity.java", "fixes": [{"line": 185, "before": "for (Object province : provinceMap.keySet()) {", "after": "for (Map.Entry<Object, Object> provinceEntry : provinceMap.entrySet()) {"}, {"line": 186, "before": "countyCityMap.put(province.toString(), province.toString());", "after": "Object province = provinceEntry.getKey();\n                countyCityMap.put(province.toString(), province.toString());"}, {"line": 188, "before": "HashMap cityMap = (HashMap) (provinceMap.get(province));", "after": "HashMap cityMap = (HashMap) (provinceEntry.getValue());"}, {"line": 189, "before": "for (Object city : cityMap.keySet()) {", "after": "for (Map.Entry<Object, Object> cityEntry : cityMap.entrySet()) {"}, {"line": 190, "before": "countyCityMap.put(city.toString(), city.toString());", "after": "Object city = cityEntry.getKey();\n                countyCityMap.put(city.toString(), city.toString());"}, {"line": 191, "before": "cityProvinceMap.put(city.toString(), province.toString());", "after": "cityProvinceMap.put(city.toString(), province.toString());"}, {"line": 192, "before": "ArrayList countyList = (ArrayList) cityMap.get(city);", "after": "ArrayList countyList = (ArrayList) cityEntry.getValue();"}], "timestamp": "2025-06-02T19:32:39.310930"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/CheckProfileServiceImpl.java", "fixes": [{"line": 200, "before": "log.error(\"[findAllAncestorProfiles]存在无限循环，请排查数据问题! checkProfiles: {}\" + checkProfiles);", "after": "log.error(\"[findAllAncestorProfiles]存在无限循环，请排查数据问题! checkProfiles: {}\", checkProfiles);"}], "timestamp": "2025-06-02T19:32:44.087927"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "fixes": [{"line": 247, "before": "BigDecimal bigDecimal = new BigDecimal(a);", "after": "BigDecimal bigDecimal = BigDecimal.valueOf(a);"}, {"line": 248, "before": "BigDecimal bigDecimal2 = new BigDecimal(2.4f);", "after": "BigDecimal bigDecimal2 = BigDecimal.valueOf(2.4f);"}, {"line": 249, "before": "BigDecimal bigDecimal3 = new BigDecimal(3.8);", "after": "BigDecimal bigDecimal3 = BigDecimal.valueOf(3.8);"}, {"line": 251, "before": "BigDecimal bigDecimal5 = BigDecimal.valueOf(a).add(new BigDecimal(b));", "after": "BigDecimal bigDecimal5 = BigDecimal.valueOf(a).add(BigDecimal.valueOf(b));"}, {"line": 255, "before": "System.out.println(a.equals(b));", "after": "System.out.println(a.compareTo(b) == 0);"}, {"line": 259, "before": "if (a.equals(b)) {", "after": "if (a.compareTo(b) == 0) {"}, {"line": 260, "before": "if (a.equals(BigDecimal.ZERO)) {", "after": "if (a.compareTo(BigDecimal.ZERO) == 0) {"}, {"line": 263, "before": "if (BigDecimal.ZERO.equals(b)) {", "after": "if (BigDecimal.ZERO.compareTo(b) == 0) {"}, {"line": 266, "before": "boolean c = a.equals(BigDecimal.ZERO);", "after": "boolean c = a.compareTo(BigDecimal.ZERO) == 0;"}, {"line": 269, "before": "boolean d = BigDecimal.ZERO.equals(b);", "after": "boolean d = BigDecimal.ZERO.compareTo(b) == 0;"}, {"line": 275, "before": "private final ThreadLocal<Object> kconfParameter = ThreadLocal.withInitial(Person::new);", "after": "private static final ThreadLocal<Object> kconfParameter = ThreadLocal.withInitial(Person::new);"}, {"line": 276, "before": "private final ThreadLocal<Object> redisData = ThreadLocal.withInitial(Person::new);", "after": "private static final ThreadLocal<Object> redisData = ThreadLocal.withInitial(Person::new);"}, {"line": 284, "before": "private final ThreadLocal<Map<String, Object>> itemLabelScoreMap = ThreadLocal.withInitial(() -> new HashMap<String, Object>() {{ put(\"adjustWatchtimeScore\", new Object()); put(\"formulaWatchtimeScore\", new Object()); put(\"clickScore\", new Object()); put(\"clickWatchtimeScore\", new Object()); put(\"pwatchtimeRankScore\", new Object()); }});", "after": "private static final ThreadLocal<Map<String, Object>> itemLabelScoreMap = ThreadLocal.withInitial(() -> new HashMap<String, Object>() {{ put(\"adjustWatchtimeScore\", new Object()); put(\"formulaWatchtimeScore\", new Object()); put(\"clickScore\", new Object()); put(\"clickWatchtimeScore\", new Object()); put(\"pwatchtimeRankScore\", new Object()); }});"}, {"line": 93, "before": "String[] a7 = (String[]) rawSet.toArray();", "after": "String[] a7 = rawSet.toArray(new String[0]);"}, {"line": 107, "before": "String[] a1 = toArray(new String[0]);", "after": "String[] a1 = toArray(new String[0]);"}, {"line": 113, "before": "long a = 5l;", "after": "long a = 5L;"}, {"line": 116, "before": "long b = 456l;", "after": "long b = 456L;"}, {"line": 120, "before": "long c = 789l;", "after": "long c = 789L;"}, {"line": 122, "before": "Long e = 456l;", "after": "Long e = 456L;"}, {"line": 126, "before": "g = 111l;", "after": "g = 111L;"}, {"line": 127, "before": "H = 222l;", "after": "H = 222L;"}, {"line": 142, "before": "catch (Exception e) { }", "after": "catch (Exception e) { // 捕获异常并忽略，原因：XXX }"}, {"line": 37, "before": "try { lock.lock(); } finally { lock.unlock(); }", "after": "lock.lock(); try { } finally { lock.unlock(); }"}], "timestamp": "2025-06-02T19:32:46.963531"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/platform/notice/PlatformKimMsgSendService.java", "fixes": [{"line": 68, "before": "log.error(\"发送消息到kim群机器人失败，无效的机器人地址：\" + robotWebhook);", "after": "log.error(\"发送消息到kim群机器人失败，无效的机器人地址：{}\", robotWebhook);"}, {"line": 105, "before": "log.error(\"发送消息到kim群机器人失败，无效的机器人地址：\" + robotWebhook);", "after": "log.error(\"发送消息到kim群机器人失败，无效的机器人地址：{}\", robotWebhook);"}], "timestamp": "2025-06-02T19:32:54.031784"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/entity/platform/FileState.java", "fixes": [{"line": 50, "before": "return String.format(\"%.2f\", (float) this.duplicationFile / this.totalFile);", "after": "return new BigDecimal(this.duplicationFile).divide(new BigDecimal(this.totalFile), 2, RoundingMode.HALF_UP).toString();"}], "timestamp": "2025-06-02T19:32:58.073513"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/common/mappers/ComplexityRepositoryMapper.java", "fixes": [{"line": 24, "before": "@Select(\"select id from complexity_repository where exec_record_id = #{execRecordId}\")", "after": "@Select(\"select id from complexity_repository where exec_record_id = #{execRecordId} limit #{limit}\")"}], "timestamp": "2025-06-02T19:33:04.362450"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/process/impl/ProcessCheckServiceImpl.java", "fixes": [{"line": 281, "before": "ksCheckLevelResult.setLevel3Result(CheckResultEnum.FAIL.name());", "after": "ksCheckLevelResult.setLevel3Result(CheckResultEnum.FAIL.name());"}, {"line": 291, "before": "Map<Long, Task> taskIdMap = tasks.stream().collect(Collectors.toMap(Task::getId, Function.identity()));", "after": "Map<Long, Task> taskIdMap = tasks.stream().collect(Collectors.toMap(Task::getId, Function.identity(), (existing, replacement) -> existing));"}, {"line": 292, "before": "Map<Long, TaskConfig> configMap = taskConfigs.stream().collect(Collectors.toMap(TaskConfig::getTaskId, o -> o));", "after": "Map<Long, TaskConfig> configMap = taskConfigs.stream().collect(Collectors.toMap(TaskConfig::getTaskId, o -> o, (existing, replacement) -> existing));"}], "timestamp": "2025-06-02T19:33:11.504936"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SqlRuleUtils.java", "fixes": [{"line": 1, "before": "private String[] values;", "after": ""}, {"line": 1, "before": "private String[] values;", "after": ""}, {"line": 1, "before": "private boolean upperInclusive;", "after": ""}, {"line": 1, "before": "private boolean lowerInclusive;", "after": ""}, {"line": 1, "before": "private boolean upperInclusive;", "after": ""}, {"line": 1, "before": "private boolean lowerInclusive;", "after": ""}, {"line": 31, "before": "this.lowerBound = lowerBound;", "after": "this.lowerBound = 0.0;"}, {"line": 32, "before": "this.upperBound = upperBound;", "after": "this.upperBound = 0.0;"}, {"line": 69, "before": "this.lowerBound = lowerBound;", "after": "this.lowerBound = 0.0;"}, {"line": 70, "before": "this.upperBound = upperBound;", "after": "this.upperBound = 0.0;"}, {"line": 71, "before": "this.lowerInclusive = lowerInclusive;", "after": "this.lowerInclusive = false;"}, {"line": 72, "before": "this.upperInclusive = upperInclusive;", "after": "this.upperInclusive = false;"}, {"line": 31, "before": "this.lowerBound = lowerBound;", "after": "this.lowerBound = 0.0;"}, {"line": 32, "before": "this.upperBound = upperBound;", "after": "this.upperBound = 0.0;"}, {"line": 33, "before": "this.lowerInclusive = lowerInclusive;", "after": "this.lowerInclusive = false;"}, {"line": 34, "before": "this.upperInclusive = upperInclusive;", "after": "this.upperInclusive = false;"}], "timestamp": "2025-06-02T19:33:11.867817"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/Department.java", "fixes": [{"line": 25, "before": "@EqualsAndHashCode(callSuper = false)", "after": "@EqualsAndHashCode(callSuper = true)"}, {"line": 31, "before": "@EqualsAndHashCode", "after": "@EqualsAndHashCode(callSuper = true)"}], "timestamp": "2025-06-02T19:33:16.814273"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/constant/analyze/LocalAnalyzeTaskEnum.java", "fixes": [{"line": 38, "before": "private String name;", "after": "private String taskName;"}, {"line": 48, "before": "ENUM_MAP.put(taskEnum.getName(), taskEnum)", "after": "ENUM_MAP.put(taskEnum.getTaskName(), taskEnum)"}, {"line": 52, "before": "return ENUM_MAP.get(name);", "after": "return ENUM_MAP.get(taskName);"}, {"line": 39, "before": "@Getter", "after": "@Getter\n@Setter\nprivate String taskName;"}, {"line": 51, "before": "public static LocalAnalyzeTaskEnum getEnumByEnumName(String name) {", "after": "public static LocalAnalyzeTaskEnum getEnumByEnumName(String taskName) {"}], "timestamp": "2025-06-02T19:33:16.871853"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/PotentialFalsePositives.java", "fixes": [{"line": 90, "before": "input.trim();", "after": "input = input.trim();"}], "timestamp": "2025-06-02T19:33:25.332629"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/mr/rule/impl/MrPomSnapshotDependencyRule.java", "fixes": [{"line": 73, "before": "issue.setIssueId((long) Math.abs(issue.getIssueUniqId().hashCode()));", "after": "int hashCode = issue.getIssueUniqId().hashCode();\nissue.setIssueId((long) (hashCode == Integer.MIN_VALUE ? Integer.MAX_VALUE : Math.abs(hashCode)));"}], "timestamp": "2025-06-02T19:33:31.289740"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/mybatis/MybatisPlusGenerator.java", "fixes": [{"line": 41, "before": "String PASSWORD = \"hardcoded_password\";", "after": "String PASSWORD = System.getenv(\"MYBATIS_DB_PASSWORD\");"}], "timestamp": "2025-06-02T19:33:41.569017"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/utils/IssueUtils.java", "fixes": [{"line": 527, "before": "Map<String, Long> map = Maps.newHashMap();", "after": "Map<String, Long> map = new HashMap<>();"}, {"line": 528, "before": "for (Map<String, Object> countMap : typeGroupList) {", "after": "for (Map<String, Object> countMap : typeGroupList) {"}, {"line": 530, "before": "map.put(mapKey, count);", "after": "map.put(mapKey, count);"}], "timestamp": "2025-06-02T19:33:49.908019"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/mr/rule/impl/MrReleaseRootPomVersionRule.java", "fixes": [{"line": 75, "before": "issue.setIssueId((long) Math.abs(issue.getIssueUniqId().hashCode()));", "after": "issue.setIssueId((long) (issue.getIssueUniqId().hashCode() & 0x7FFFFFFF));"}], "timestamp": "2025-06-02T19:33:58.750281"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/datasource/ThemisDataSourceMapSetter.java", "fixes": [{"line": 56, "before": "String PASSWORD = \"hardcoded_password\";", "after": "String PASSWORD = System.getenv(\"DATASOURCE_PASSWORD\");"}, {"line": 103, "before": "dataSource.setConfig(\"shardingsphere_config\", \"some_value\");", "after": "dataSource.setConfig(\"shardingsphere_config\", loadConfigFromExternalSource());"}, {"line": 11, "before": "private static final String SHARDINGSPHERE_CONFIG = \"shardingsphere_config\";", "after": "private static final String SHARDINGSPHERE_CONFIG = System.getenv(\"SHARDINGSPHERE_CONFIG\");"}, {"line": 17, "before": "String configValue = \"default_value\";", "after": "String configValue = loadConfigFromExternalSourceOrDefault(\"default_value\");"}], "timestamp": "2025-06-02T19:34:02.237153"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/platform/skyeye/impl/FolderMaintainabilityServiceImpl.java", "fixes": [{"line": 74, "before": "valueSumOfFolder.put(fileId, valueSumOfFolder.getOrDefault(fileId, 0.0) + Double.parseDouble(fileMeasure.getMetricValue()));", "after": "valueSumOfFolder.put(fileId, valueSumOfFolder.getOrDefault(fileId, BigDecimal.ZERO).add(new BigDecimal(fileMeasure.getMetricValue())));"}, {"line": 76, "before": "valueSumOfFolder.entrySet().forEach(e -> e.setValue(e.getValue() / fileNumOfFolder.get(e.getKey())));", "after": "valueSumOfFolder.entrySet().forEach(e -> e.setValue(e.getValue().divide(BigDecimal.valueOf(fileNumOfFolder.get(e.getKey())), 2, RoundingMode.HALF_UP)));"}], "timestamp": "2025-06-02T19:34:04.706081"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/sonar/mode/ScanModeService.java", "fixes": [{"line": 63, "before": "Map<Long, PCheckBase> pCheckBaseMap = pCheckBases.stream().collect(Collectors.toMap(PCheckBase::getId, Function.identity()));", "after": "Map<Long, PCheckBase> pCheckBaseMap = pCheckBases.stream().collect(Collectors.toMap(PCheckBase::getId, Function.identity(), (existing, replacement) -> existing));"}], "timestamp": "2025-06-02T19:34:08.682534"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/common/mappers/CheckRuleMapper.java", "fixes": [{"line": 24, "before": "@Select(\"select rule_key from check_rule where deleted = 0 and can_skip = 0\")", "after": "@Select(\"select rule_key from check_rule where deleted = 0 and can_skip = 0 limit 100\")"}], "timestamp": "2025-06-02T19:34:12.826196"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/check/QualityTaskKspSyncService.java", "fixes": [{"line": 162, "before": "countDownLatch.await();", "after": "countDownLatch.await(5, TimeUnit.MINUTES);"}, {"line": 120, "before": "log.info(taskStatusName + \" status taskList is empty,skip this scan\");", "after": "log.info(\"{} status taskList is empty,skip this scan\", taskStatusName);"}, {"line": 130, "before": "Map<Long, Task> taskIdTaskMap = taskList.stream().collect(Collectors.toMap(Task::getId, Function.identity()));", "after": "Map<Long, Task> taskIdTaskMap = taskList.stream().collect(Collectors.toMap(Task::getId, Function.identity(), (existing, replacement) -> existing));"}, {"line": 132, "before": "Map<Long, TaskConfig> taskIdConfigMap = taskConfigList.stream().collect(Collectors.toMap(TaskConfig::getTaskId, Function.identity()));", "after": "Map<Long, TaskConfig> taskIdConfigMap = taskConfigList.stream().collect(Collectors.toMap(TaskConfig::getTaskId, Function.identity(), (existing, replacement) -> existing));"}], "timestamp": "2025-06-02T19:34:15.501541"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/client/compile/CompileApi.java", "fixes": [{"line": 70, "before": "if (!CompileApiRsp.success(deserialize) || Objects.isNull(deserialize.getData())) {", "after": "if (deserialize == null || !CompileApiRsp.success(deserialize) || Objects.isNull(deserialize.getData())) {"}, {"line": 97, "before": "if (!CompileApiRsp.success(deserialize) || Objects.isNull(deserialize.getData())) {", "after": "if (deserialize == null || !CompileApiRsp.success(deserialize) || Objects.isNull(deserialize.getData())) {"}], "timestamp": "2025-06-02T19:34:19.652395"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/platform/impl/CoverityServiceImpl.java", "fixes": [{"line": 151, "before": "coverityAgentService.getByIpAndName(req.getIp(), req.getName());", "after": "coverityAgentService.getByIpResult(CommonUtils.getIpResult(req.getIp()), req.getName());"}, {"line": 300, "before": "for (Object key : map.keySet()) {", "after": "for (Map.Entry<Object, Object> entry : map.entrySet()) {"}, {"line": 301, "before": "Object value = map.get(key);", "after": "Object value = entry.getValue();"}], "timestamp": "2025-06-02T19:34:22.240539"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/LocationEntity.java", "fixes": [{"line": 185, "before": "for (Object key : provinceMap.keySet()) {", "after": "for (Map.Entry<Object, Object> provinceEntry : provinceMap.entrySet()) {"}, {"line": 186, "before": "Object province = key;", "after": "Object province = provinceEntry.getKey();"}, {"line": 187, "before": "HashMap cityMap = (HashMap) (provinceMap.get(province));", "after": "HashMap cityMap = (HashMap) (provinceEntry.getValue());"}], "timestamp": "2025-06-02T19:34:25.215534"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/CheckProfileServiceImpl.java", "fixes": [{"line": 200, "before": "log.error(\"[findAllAncestorProfiles]存在无限循环，请排查数据问题! checkProfiles: \" + checkProfiles);", "after": "log.error(\"[findAllAncestorProfiles]存在无限循环，请排查数据问题! checkProfiles: {}\", checkProfiles);"}], "timestamp": "2025-06-02T19:34:29.800808"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/common/mappers/PCheckIssueMapper.java", "fixes": [{"line": 33, "before": "@Select(\"SELECT distinct(execution_refer_type) FROM p_check_issue where p_base_id = #{pBaseId} order by execution_refer_type\")", "after": "@Select(\"SELECT distinct(execution_refer_type) FROM p_check_issue where p_base_id = #{pBaseId} order by execution_refer_type limit 100\")"}], "timestamp": "2025-06-02T19:34:33.293860"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/platform/notice/PlatformKimMsgSendService.java", "fixes": [{"line": 68, "before": "log.error(\"发送消息到kim群机器人失败，无效的机器人地址：\" + robotWebhook);", "after": "log.error(\"发送消息到kim群机器人失败，无效的机器人地址：{}\", robotWebhook);"}, {"line": 105, "before": "log.error(\"发送消息到kim群机器人失败，无效的机器人地址：\" + robotWebhook);", "after": "log.error(\"发送消息到kim群机器人失败，无效的机器人地址：{}\", robotWebhook);"}], "timestamp": "2025-06-02T19:34:34.821461"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/entity/platform/FileState.java", "fixes": [{"line": 50, "before": "return new BigDecimal(this.duplicationFile).divide(new BigDecimal(this.totalFile), 2, RoundingMode.HALF_UP).toString();", "after": "if (this.totalFile == 0) {\n    return \"0.00\";\n}\nreturn new BigDecimal(this.duplicationFile).divide(new BigDecimal(this.totalFile), 2, RoundingMode.HALF_UP).toString();"}], "timestamp": "2025-06-02T19:34:39.693688"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/CustomRuleDemoV1.java", "fixes": [{"line": 15, "before": "    @Data\n    class ExpressStateIcon {", "after": "    @Data\n    static class ExpressStateIcon {"}], "timestamp": "2025-06-02T19:34:40.247113"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/mr/rule/impl/MrLogbackPackagingDataRule.java", "fixes": [{"line": 118, "before": "issue.setIssueId((long) Math.abs(issue.getIssueUniqId().hashCode()));", "after": "int hashCode = issue.getIssueUniqId().hashCode();\nissue.setIssueId((long) (hashCode == Integer.MIN_VALUE ? Integer.MAX_VALUE : Math.abs(hashCode)));"}, {"line": 54, "before": "public void setDocumentLocator(Locator locator) {", "after": "@Override\npublic void setDocumentLocator(Locator locator) {"}], "timestamp": "2025-06-02T19:34:45.985538"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/proxy/algorithm/ConsistentHashingWithVirtualNode.java", "fixes": [{"line": 63, "before": "int hash = str.hashCode() % 4;", "after": "int hash = Math.abs((long) str.hashCode()) % 4;"}], "timestamp": "2025-06-02T19:34:49.938342"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/process/impl/ProcessCheckServiceImpl.java", "fixes": [{"line": 281, "before": "ksCheckLevelResult.setLevel1Result(CheckResultEnum.SCANNING.name());", "after": "ksCheckLevelResult.setLevel1Result(CheckResultEnum.SCANNING.name());\nbreak;"}, {"line": 291, "before": "Map<Long, Task> taskIdMap = tasks.stream().collect(Collectors.toMap(Task::getId, Function.identity(), (existing, replacement) -> existing));", "after": "Map<Long, Task> taskIdMap = tasks.stream().collect(Collectors.toMap(Task::getId, Function.identity(), (existing, replacement) -> existing));"}, {"line": 292, "before": "Map<Long, TaskConfig> configMap = taskConfigs.stream().collect(Collectors.toMap(TaskConfig::getTaskId, o -> o, (existing, replacement) -> existing));", "after": "Map<Long, TaskConfig> configMap = taskConfigs.stream().collect(Collectors.toMap(TaskConfig::getTaskId, o -> o, (existing, replacement) -> existing));"}], "timestamp": "2025-06-02T19:34:52.866924"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/proxy/algorithm/ConsistentHashingWithoutVirtualNode.java", "fixes": [{"line": 61, "before": "return str.hashCode() % 4; //hash(key) % n", "after": "return Math.abs((long) str.hashCode()) % 4; // 确保分片结果为非负值"}], "timestamp": "2025-06-02T19:34:54.488156"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/mr/rule/impl/MrReleaseRootPomVersionRule.java", "fixes": [{"line": 75, "before": "issue.setIssueId((long) (issue.getIssueUniqId().hashCode() & 0x7FFFFFFF));", "after": "issue.setIssueId((long) (Math.abs(issue.getIssueUniqId().hashCode())));"}], "timestamp": "2025-06-02T19:35:02.633383"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/common/mappers/IssueSummaryBaseMapper.java", "fixes": [{"line": 23, "before": "@Select(\"select issue_uniq_id from issue_summary_base where issue_uniq_id in (#{issueUniqIds})\")", "after": "@Select(\"select issue_uniq_id from issue_summary_base where issue_uniq_id in (#{issueUniqIds}) limit 100\")"}], "timestamp": "2025-06-02T19:35:04.012053"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/common/mappers/ComplexityFileMapper.java", "fixes": [{"line": 24, "before": "@Select(\"select id from complexity_file where repository_id = #{repositoryId}\")", "after": "@Select(\"select id from complexity_file where repository_id = #{repositoryId} limit 100\")"}], "timestamp": "2025-06-02T19:35:08.164160"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/platform/skyeye/impl/FolderMaintainabilityServiceImpl.java", "fixes": [{"line": 74, "before": "valueSumOfFolder.put(fileId, valueSumOfFolder.getOrDefault(fileId, 0.0) + Double.parseDouble(fileMeasure.getMetricValue()));", "after": "valueSumOfFolder.put(fileId, valueSumOfFolder.getOrDefault(fileId, BigDecimal.ZERO).add(new BigDecimal(fileMeasure.getMetricValue())));"}, {"line": 76, "before": "valueSumOfFolder.entrySet().forEach(e -> e.setValue(e.getValue().divide(BigDecimal.valueOf(fileNumOfFolder.get(e.getKey())), 2, RoundingMode.HALF_UP)));", "after": "valueSumOfFolder.entrySet().forEach(e -> e.setValue(e.getValue().divide(BigDecimal.valueOf(fileNumOfFolder.get(e.getKey())), 2, RoundingMode.HALF_UP)));"}], "timestamp": "2025-06-02T19:35:08.754658"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/IssueSummaryServiceImpl.java", "fixes": [{"line": 267, "before": "return issue2SummaryListMap.entrySet().stream().collect(Collectors.toMap(Entry::getKey, e -> e.getValue().get(0)));", "after": "return issue2SummaryListMap.entrySet().stream().collect(Collectors.toMap(Entry::getKey, e -> e.getValue().get(0), (existing, replacement) -> existing));"}, {"line": 288, "before": "return issueV2ToSummaryListMap.entrySet().stream().collect(Collectors.toMap(Entry::getKey, e -> e.getValue().get(0)));", "after": "return issueV2ToSummaryListMap.entrySet().stream().collect(Collectors.toMap(Entry::getKey, e -> e.getValue().get(0), (existing, replacement) -> existing));"}], "timestamp": "2025-06-02T19:35:14.343398"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/check/QualityTaskKspSyncService.java", "fixes": [{"line": 162, "before": "countDownLatch.await(5, TimeUnit.MINUTES);", "after": "boolean awaitResult = countDownLatch.await(5, TimeUnit.MINUTES);\nif (!awaitResult) {\n    log.warn(\"CountDownLatch await timed out after 5 minutes\");\n}"}, {"line": 120, "before": "log.info(String.format(\"Empty kdev pipeline records[status='%s'], finish scan\", statusEnum.getDesc()));", "after": "log.info(\"Empty kdev pipeline records[status='{}'], finish scan\", statusEnum.getDesc());"}, {"line": 130, "before": "Map<Long, Task> taskIdTaskMap = taskList.stream().collect(Collectors.toMap(Task::getId, Function.identity(), (existing, replacement) -> existing));", "after": "Map<Long, Task> taskIdTaskMap = taskList.stream().collect(Collectors.toMap(Task::getId, Function.identity(), (existing, replacement) -> {\n    log.warn(\"Duplicate key detected in taskIdTaskMap: {}\", existing.getId());\n    return existing;\n}));"}, {"line": 132, "before": "Map<Long, TaskConfig> taskIdConfigMap = taskConfigList.stream().collect(Collectors.toMap(TaskConfig::getTaskId, Function.identity()));", "after": "Map<Long, TaskConfig> taskIdConfigMap = taskConfigList.stream().collect(Collectors.toMap(TaskConfig::getTaskId, Function.identity(), (existing, replacement) -> {\n    log.warn(\"Duplicate key detected in taskIdConfigMap: {}\", existing.getTaskId());\n    return existing;\n}));"}], "timestamp": "2025-06-02T19:35:17.083444"}
{"file": "ks-serveree-themis-runner/src/main/java/com/kuaishou/serveree/themis/runner/task/KspProductAddLabelTask.java", "fixes": [{"line": 82, "before": "countDownLatch.await();", "after": "countDownLatch.await(5, TimeUnit.SECONDS);"}, {"line": 62, "before": "public String name() {", "after": "@Override\n    public String name() {"}], "timestamp": "2025-06-02T19:35:19.022043"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/platform/impl/PlatformRuleServiceImpl.java", "fixes": [{"line": 405, "before": "updateRules.stream().collect(Collectors.toMap(CheckRuleVo::getRuleId, Function.identity(), (a, b) -> b));", "after": "updateRules.stream().collect(Collectors.toMap(CheckRuleVo::getRuleId, Function.identity(), (existing, replacement) -> replacement));"}, {"line": 1140, "before": "relations.stream().collect(Collectors.toMap(CheckProfileRuleRelation::getRuleKey, Function.identity(), (v1, v2) -> v1));", "after": "relations.stream().collect(Collectors.toMap(CheckProfileRuleRelation::getRuleKey, Function.identity(), (existing, replacement) -> existing));"}], "timestamp": "2025-06-02T19:35:26.095598"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/platform/impl/CoverityServiceImpl.java", "fixes": [{"line": 151, "before": "String ipAddress = NetworkUtils.getMainSiteIpV6();", "after": "String ipAddress = NetworkUtils.getMainSiteIpV4();"}], "timestamp": "2025-06-02T19:35:26.219415"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/common/mappers/PCheckIssueMapper.java", "fixes": [{"line": 33, "before": "@Select(\"SELECT distinct(execution_refer_type) FROM p_check_issue where p_base_id = #{pBaseId} order by execution_refer_type\")", "after": "@Select(\"SELECT distinct(execution_refer_type) FROM p_check_issue where p_base_id = #{pBaseId} order by execution_refer_type limit 100\")"}], "timestamp": "2025-06-02T19:35:30.853050"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/constant/platform/PlatformLanguageEnum.java", "fixes": [{"line": 45, "before": "private final String name;", "after": "private final String languageName;"}, {"line": 63, "before": "NAME_MAP.put(platformLanguageEnum.getName(), platformLanguageEnum);", "after": "NAME_MAP.put(platformLanguageEnum.getLanguageName(), platformLanguageEnum);"}, {"line": 66, "before": "public static PlatformLanguageEnum getEnumByName(String name) {", "after": "public static PlatformLanguageEnum getEnumByLanguageName(String languageName) {"}, {"line": 67, "before": "return NAME_MAP.get(name);", "after": "return NAME_MAP.get(languageName);"}, {"line": 62, "before": "for (PlatformLanguageEnum platformLanguageEnum : PlatformLanguageEnum.values()) {", "after": "for (Map.Entry<String, PlatformLanguageEnum> entry : NAME_MAP.entrySet()) {"}, {"line": 63, "before": "NAME_MAP.put(platformLanguageEnum.getName(), platformLanguageEnum);", "after": "String key = entry.getKey(); PlatformLanguageEnum value = entry.getValue(); NAME_MAP.put(key, value);"}], "timestamp": "2025-06-02T19:35:33.414095"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/CustomRuleDemoV1.java", "fixes": [{"line": 15, "before": "    @Data\n    class ExpressStateIcon {", "after": "    @Data\n    static class ExpressStateIcon {"}], "timestamp": "2025-06-02T19:35:34.931147"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/utils/HiveJdbcUtil.java", "fixes": [{"line": 86, "before": "connection = DriverManager.getConnection(url, \"sspHiveUserName.get()\", \"\");", "after": "connection = DriverManager.getConnection(url, \"sspHiveUserName.get()\", getPasswordFromSecureStorage());"}, {"line": 57, "before": "catch (SQLException e) {", "after": "catch (SQLException e) {\n    // 捕获异常并忽略，原因：此处异常不会影响后续逻辑"}, {"line": 72, "before": "public void process(WatchedEvent watchedEvent) {", "after": "@Override\npublic void process(WatchedEvent watchedEvent) {"}, {"line": 46, "before": "if (statement != null) {", "after": "// 移除冗余的Null检查，因为statement之前已经被解引用\n// if (statement != null) {"}], "timestamp": "2025-06-02T19:35:41.612281"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/proxy/algorithm/CodeScanningHashUtil.java", "fixes": [{"line": 54, "before": "int hash = str.hashCode() % serverNum;", "after": "long hash = Math.abs((long) str.hashCode()) % serverNum;"}], "timestamp": "2025-06-02T19:35:46.707435"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/mr/rule/impl/MrLogbackPackagingDataRule.java", "fixes": [{"line": 54, "before": "public void setDocumentLocator(Locator locator) {", "after": "@Override\npublic void setDocumentLocator(Locator locator) {"}, {"line": 118, "before": "int hashCode = issue.getIssueUniqId().hashCode();", "after": "long hashCode = issue.getIssueUniqId().hashCode();"}, {"line": 119, "before": "issue.setIssueId((long) (hashCode == Integer.MIN_VALUE ? Integer.MAX_VALUE : Math.abs(hashCode)));", "after": "issue.setIssueId(hashCode == Integer.MIN_VALUE ? Integer.MAX_VALUE : Math.abs(hashCode));"}], "timestamp": "2025-06-02T19:35:49.126791"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/common/mappers/ThemisAnalyzeIssueMapper.java", "fixes": [{"line": 26, "before": "@Select(\"SELECT COUNT(0) FROM (SELECT illegal_file FROM themis_analyze_issue WHERE (analyze_id = #{analyzeId} AND rule_type = #{ruleType}) GROUP BY illegal_file) TOTAL\")", "after": "@Select(\"SELECT COUNT(0) FROM (SELECT illegal_file FROM themis_analyze_issue WHERE (analyze_id = #{analyzeId} AND rule_type = #{ruleType}) GROUP BY illegal_file LIMIT 100) TOTAL\")"}], "timestamp": "2025-06-02T19:35:53.325867"}
{"file": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/proxy/algorithm/ConsistentHashingWithVirtualNode.java", "fixes": [{"line": 63, "before": "int hash = Math.abs((long) str.hashCode()) % 4;", "after": "int hash = (int) (Math.abs((long) str.hashCode()) % 4);"}], "timestamp": "2025-06-02T19:35:53.690579"}
