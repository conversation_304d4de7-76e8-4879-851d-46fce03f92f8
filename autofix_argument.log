# Autofix Argument Log
# 记录代码质量问题修复操作

## 修复进度跟踪
开始时间: $(date)

## 待处理文件列表
1. ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java
2. ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java
3. ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SqlRuleUtils.java
4. ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/Department.java
5. ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/PotentialFalsePositives.java
6. ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/mr/rule/impl/MrPomSnapshotDependencyRule.java
7. ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/mybatis/MybatisPlusGenerator.java
8. ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/utils/IssueUtils.java
9. ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/datasource/ThemisDataSourceMapSetter.java
10. ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/sonar/mode/ScanModeService.java
... (更多文件)

## 修复记录
{"file": "demo_files/BigDecimalDemo.java", "fixes": [{"line": 6, "issue": "squid:S2111", "message": "Use \"BigDecimal.valueOf\" instead.", "fix": "Replace 'new BigDecimal(price)' with 'BigDecimal.valueOf(price)'.", "codeChange": "BigDecimal priceDecimal = BigDecimal.valueOf(price);"}, {"line": 7, "issue": "findbugs:DMI_BIGDECIMAL_CONSTRUCTED_FROM_DOUBLE", "message": "BigDecimal constructed from double", "fix": "Replace 'new BigDecimal(tax)' with 'BigDecimal.valueOf(tax)'.", "codeChange": "BigDecimal taxDecimal = BigDecimal.valueOf(tax);"}], "timestamp": "2025-06-02T18:50:43.581979"}
{"file": "test_quick.java", "fixes": [{"filePath": "test_quick.java", "line": 4, "issue": "squid:S2111", "message": "Use \"BigDecimal.valueOf\" instead.", "fix": {"description": "Replace the constructor call 'new BigDecimal(value)' with 'BigDecimal.valueOf(value)' to avoid precision issues.", "updatedCode": "BigDecimal bd = BigDecimal.valueOf(value);"}}], "timestamp": "2025-06-02T18:53:23.092000"}
{"file": "test_quick.java", "fixes": [{"filePath": "test_quick.java", "line": 4, "issue": "squid:S2111", "description": "Replace the usage of 'new BigDecimal(value)' with 'BigDecimal.valueOf(value)' to avoid precision issues caused by floating-point representation.", "fix": {"oldCode": "BigDecimal bd = new BigDecimal(value);", "newCode": "BigDecimal bd = BigDecimal.valueOf(value);"}}], "timestamp": "2025-06-02T18:53:50.506945"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/LocationEntity.java", "fixes": [{"issue": "findbugs:WMI_WRONG_MAP_ITERATOR", "location": {"filePath": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/LocationEntity.java", "line": 185}, "fix": {"description": "Replace the inefficient use of keySet iterator with entrySet iterator in the static initializer block of ProvinceCityMapping.", "codeChange": {"before": "for (Object province : provinceMap.keySet()) {\n    countyCityMap.put(province.toString(), province.toString());\n\n    HashMap cityMap = (HashMap) (provinceMap.get(province));\n    for (Object city : cityMap.keySet()) {\n        countyCityMap.put(city.toString(), city.toString());\n        cityProvinceMap.put(city.toString(), province.toString());\n        ArrayList countyList = (ArrayList) cityMap.get(city);\n        for (Object county : countyList) {\n            cityProvinceMap.put(county.toString(), province.toString());\n            countyCityMap.put(county.toString(), city.toString());\n        }\n    }\n}", "after": "for (Map.Entry<Object, Object> provinceEntry : provinceMap.entrySet()) {\n    Object province = provinceEntry.getKey();\n    countyCityMap.put(province.toString(), province.toString());\n\n    HashMap cityMap = (HashMap) provinceEntry.getValue();\n    for (Map.Entry<Object, Object> cityEntry : cityMap.entrySet()) {\n        Object city = cityEntry.getKey();\n        countyCityMap.put(city.toString(), city.toString());\n        cityProvinceMap.put(city.toString(), province.toString());\n        ArrayList countyList = (ArrayList) cityEntry.getValue();\n        for (Object county : countyList) {\n            cityProvinceMap.put(county.toString(), province.toString());\n            countyCityMap.put(county.toString(), city.toString());\n        }\n    }\n}"}}}], "timestamp": "2025-06-02T19:10:55.971653"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/LocationEntity.java", "fixes": [{"issue": "findbugs:WMI_WRONG_MAP_ITERATOR", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/LocationEntity.java", "line": 185, "description": "在静态初始化块中，使用了 keySet 的迭代器来访问 Map 的键和值，这种方式效率较低，建议改为使用 entrySet 的迭代器。", "fix": {"before": "for (Object province : provinceMap.keySet()) {\n    countyCityMap.put(province.toString(), province.toString());\n\n    HashMap cityMap = (HashMap) (provinceMap.get(province));\n    for (Object city : cityMap.keySet()) {\n        countyCityMap.put(city.toString(), city.toString());\n        cityProvinceMap.put(city.toString(), province.toString());\n        ArrayList countyList = (ArrayList) cityMap.get(city);\n        for (Object county : countyList) {\n            cityProvinceMap.put(county.toString(), province.toString());\n            countyCityMap.put(county.toString(), city.toString());\n        }\n    }\n}", "after": "for (Map.Entry<Object, Object> provinceEntry : provinceMap.entrySet()) {\n    Object province = provinceEntry.getKey();\n    HashMap cityMap = (HashMap) provinceEntry.getValue();\n\n    countyCityMap.put(province.toString(), province.toString());\n\n    for (Map.Entry<Object, Object> cityEntry : cityMap.entrySet()) {\n        Object city = cityEntry.getKey();\n        ArrayList countyList = (ArrayList) cityEntry.getValue();\n\n        countyCityMap.put(city.toString(), city.toString());\n        cityProvinceMap.put(city.toString(), province.toString());\n\n        for (Object county : countyList) {\n            cityProvinceMap.put(county.toString(), province.toString());\n            countyCityMap.put(county.toString(), city.toString());\n        }\n    }\n}"}}], "timestamp": "2025-06-02T19:11:37.713360"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/LocationEntity.java", "fixes": [{"line": 185, "before": "for (Object province : provinceMap.keySet()) {", "after": "for (Map.Entry<Object, Object> provinceEntry : provinceMap.entrySet()) {"}, {"line": 189, "before": "for (Object city : cityMap.keySet()) {", "after": "for (Map.Entry<Object, Object> cityEntry : cityMap.entrySet()) {"}, {"line": 190, "before": "countyCityMap.put(city.toString(), city.toString());", "after": "countyCityMap.put(cityEntry.getKey().toString(), cityEntry.getKey().toString());"}, {"line": 191, "before": "cityProvinceMap.put(city.toString(), province.toString());", "after": "cityProvinceMap.put(cityEntry.getKey().toString(), provinceEntry.getKey().toString());"}, {"line": 192, "before": "ArrayList countyList = (ArrayList) cityMap.get(city);", "after": "ArrayList countyList = (ArrayList) cityEntry.getValue();"}], "timestamp": "2025-06-02T19:15:23.075974"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/LocationEntity.java", "fixes": [{"line": 185, "before": "for (Object province : provinceMap.keySet()) {", "after": "for (Map.Entry<Object, Object> provinceEntry : provinceMap.entrySet()) {"}, {"line": 188, "before": "for (Object city : cityMap.keySet()) {", "after": "for (Map.Entry<Object, Object> cityEntry : cityMap.entrySet()) {"}, {"line": 189, "before": "ArrayList countyList = (ArrayList) cityMap.get(city);", "after": "ArrayList countyList = (ArrayList) cityEntry.getValue();"}], "timestamp": "2025-06-02T19:16:34.675346"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "fixes": [{"line": 247, "before": "BigDecimal bigDecimal = new BigDecimal(a);", "after": "BigDecimal bigDecimal = BigDecimal.valueOf(a);"}, {"line": 248, "before": "BigDecimal bigDecimal2 = new BigDecimal(2.4f);", "after": "BigDecimal bigDecimal2 = BigDecimal.valueOf(2.4f);"}, {"line": 249, "before": "BigDecimal bigDecimal3 = new BigDecimal(3.8);", "after": "BigDecimal bigDecimal3 = BigDecimal.valueOf(3.8);"}, {"line": 251, "before": "BigDecimal bigDecimal5 = new BigDecimal(a + b);", "after": "BigDecimal bigDecimal5 = BigDecimal.valueOf(a).add(BigDecimal.valueOf(b));"}, {"line": 255, "before": "return a.equals(b);", "after": "return a.compareTo(b) == 0;"}, {"line": 259, "before": "if (a.equals(b)) {", "after": "if (a.compareTo(b) == 0) {"}, {"line": 260, "before": "if (a.equals(BigDecimal.ZERO)) {", "after": "if (a.compareTo(BigDecimal.ZERO) == 0) {"}, {"line": 263, "before": "if (!BigDecimal.ZERO.equals(b)) {", "after": "if (BigDecimal.ZERO.compareTo(b) != 0) {"}, {"line": 266, "before": "boolean c = a.equals(BigDecimal.ZERO);", "after": "boolean c = a.compareTo(BigDecimal.ZERO) == 0;"}, {"line": 269, "before": "boolean d = BigDecimal.ZERO.equals(b);", "after": "boolean d = BigDecimal.ZERO.compareTo(b) == 0;"}, {"line": 270, "before": "boolean d = BigDecimal.ZERO.equals(b);", "after": "boolean d = BigDecimal.ZERO.compareTo(b) == 0;"}, {"line": 275, "before": "private final ThreadLocal<Object> kconfParameter = ThreadLocal.withInitial(Person::new);", "after": "private static final ThreadLocal<Object> kconfParameter = ThreadLocal.withInitial(Person::new);"}, {"line": 276, "before": "private final ThreadLocal<Object> redisData = ThreadLocal.withInitial(Person::new);", "after": "private static final ThreadLocal<Object> redisData = ThreadLocal.withInitial(Person::new);"}, {"line": 284, "before": "private final ThreadLocal<Map<String, Object>> itemLabelScoreMap = ThreadLocal.withInitial(() -> new HashMap<String, Object>() {{ put(\"adjustWatchtimeScore\", new Object()); put(\"formulaWatchtimeScore\", new Object()); put(\"clickScore\", new Object()); put(\"clickWatchtimeScore\", new Object()); put(\"pwatchtimeRankScore\", new Object()); }});", "after": "private static final ThreadLocal<Map<String, Object>> itemLabelScoreMap = ThreadLocal.withInitial(() -> new HashMap<String, Object>() {{ put(\"adjustWatchtimeScore\", new Object()); put(\"formulaWatchtimeScore\", new Object()); put(\"clickScore\", new Object()); put(\"clickWatchtimeScore\", new Object()); put(\"pwatchtimeRankScore\", new Object()); }});"}, {"line": 93, "before": "String[] a1 = (String[]) listOfString.toArray();", "after": "String[] a1 = listOfString.toArray(new String[0]);"}, {"line": 107, "before": "String[] a1 = (String[]) toArray();", "after": "String[] a1 = toArray(new String[0]);"}, {"line": 113, "before": "private long f3 = 3l;", "after": "private long f3 = 3L;"}, {"line": 116, "before": "f1 = 123l;", "after": "f1 = 123L;"}, {"line": 120, "before": "long b = 456l;", "after": "long b = 456L;"}, {"line": 122, "before": "Long e = 456l;", "after": "Long e = 456L;"}, {"line": 126, "before": "g = 111l;", "after": "g = 111L;"}, {"line": 127, "before": "H = 222l;", "after": "H = 222L;"}, {"line": 142, "before": "catch (Exception e) {", "after": "catch (Exception e) { // Exception ignored intentionally due to specific business logic"}, {"line": 167, "before": "if (triggerTime >= MoreObjects.firstNonNull(Long.valueOf(oldRights.getTriggerCron()), 0L)) {", "after": "if (oldRights != null && triggerTime >= MoreObjects.firstNonNull(Long.valueOf(oldRights.getTriggerCron()), 0L)) {"}, {"line": 37, "before": "try { lock.lock();", "after": "lock.lock(); try {"}, {"line": 40, "before": "try { lock.lock();", "after": "lock.lock(); try {"}, {"line": 42, "before": "try { lock.lock();", "after": "lock.lock(); try {"}, {"line": 55, "before": "try { lock.lock();", "after": "lock.lock(); try {"}, {"line": 51, "before": "try { lock.lock();", "after": "lock.lock(); try {"}, {"line": 65, "before": "try { lock.lock();", "after": "lock.lock(); try {"}, {"line": 61, "before": "try { lock.lock();", "after": "lock.lock(); try {"}, {"line": 69, "before": "try { lock.lock();", "after": "lock.lock(); try {"}, {"line": 28, "before": "public boolean equals(Object obj) {", "after": "public boolean equals(Object obj) { if (!(obj instanceof SubClassTest)) return false; return super.equals(obj); }"}], "timestamp": "2025-06-02T19:17:25.370789"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java", "fixes": [{"line": 49, "before": "private static String location = \"https://abc.shkwai.com/dfkal/we/4fg;s/fsldf/fls/sss.svg\"; // Noncompliant", "after": "private static String location = System.getenv(\"CDN_LOCATION_URL\"); // Compliant"}, {"line": 50, "before": "private static String domain = \"play-safe.app\"; // Noncompliant", "after": "private static String domain = System.getenv(\"CDN_DOMAIN\"); // Compliant"}], "timestamp": "2025-06-02T19:18:49.820135"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java", "fixes": [{"line": 49, "before": "private static String domain = \"play-safe.app\"; // Noncompliant", "after": "private static String domain = System.getenv(\"CDN_DOMAIN_URL\"); // Compliant"}], "timestamp": "2025-06-02T19:19:17.385830"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java", "fixes": [{"line": 49, "before": "private static String domain = \"play-safe.app\"; // Noncompliant", "after": "private static String domain = System.getenv(\"CDN_DOMAIN_URL\"); // Compliant"}], "timestamp": "2025-06-02T19:19:48.725914"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/Department.java", "fixes": [{"line": 25, "before": "@EqualsAndHashCode(callSuper = false)", "after": "@EqualsAndHashCode(callSuper = true)"}, {"line": 31, "before": "@EqualsAndHashCode", "after": "@EqualsAndHashCode(callSuper = true)"}], "timestamp": "2025-06-02T19:20:04.270218"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/Department.java", "fixes": [{"line": 25, "before": "@EqualsAndHashCode(callSuper = false)", "after": "@EqualsAndHashCode(callSuper = true)"}, {"line": 31, "before": "@EqualsAndHashCode", "after": "@EqualsAndHashCode(callSuper = true)"}], "timestamp": "2025-06-02T19:20:45.409627"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SqlRuleUtils.java", "fixes": [{"line": 31, "before": "this.lowerBound = lowerBound;", "after": "this.lowerBound = values.length > 0 ? Double.parseDouble(values[0]) : 0.0;"}, {"line": 32, "before": "this.upperBound = upperBound;", "after": "this.upperBound = values.length > 1 ? Double.parseDouble(values[1]) : 0.0;"}, {"line": 33, "before": "this.lowerInclusive = lowerInclusive;", "after": "this.lowerInclusive = false;"}, {"line": 34, "before": "this.upperInclusive = upperInclusive;", "after": "this.upperInclusive = false;"}, {"line": 69, "before": "this.lowerBound = lowerBound;", "after": "this.lowerBound = values.length > 0 ? Double.parseDouble(values[0]) : 0.0;"}, {"line": 70, "before": "this.upperBound = upperBound;", "after": "this.upperBound = values.length > 1 ? Double.parseDouble(values[1]) : 0.0;"}, {"line": 71, "before": "this.lowerInclusive = lowerInclusive;", "after": "this.lowerInclusive = false;"}, {"line": 72, "before": "this.upperInclusive = upperInclusive;", "after": "this.upperInclusive = false;"}, {"line": 6, "before": "private String[] values;", "after": "// Removed unused field 'values' as it is not utilized in the class\n// private String[] values;"}, {"line": 6, "before": "private boolean lowerInclusive;", "after": "// Removed unused field 'lowerInclusive' as it is not utilized in the class\n// private boolean lowerInclusive;"}, {"line": 6, "before": "private boolean upperInclusive;", "after": "// Removed unused field 'upperInclusive' as it is not utilized in the class\n// private boolean upperInclusive;"}], "timestamp": "2025-06-02T19:21:03.896136"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/LocationEntity.java", "fixes": [{"line": 185, "before": "for (Object province : provinceMap.keySet()) {", "after": "for (Map.Entry<Object, Object> provinceEntry : provinceMap.entrySet()) {"}, {"line": 188, "before": "HashMap cityMap = (HashMap) (provinceMap.get(province));", "after": "HashMap cityMap = (HashMap) (provinceEntry.getValue());"}], "timestamp": "2025-06-02T19:22:33.333093"}
{"file": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/LocationEntity.java", "fixes": [{"line": 185, "before": "for (Object province : provinceMap.keySet()) {", "after": "for (Map.Entry<Object, Object> provinceEntry : provinceMap.entrySet()) {"}, {"line": 188, "before": "HashMap cityMap = (HashMap) (provinceMap.get(province));", "after": "HashMap cityMap = (HashMap) (provinceEntry.getValue());"}], "timestamp": "2025-06-02T19:24:34.178249"}
