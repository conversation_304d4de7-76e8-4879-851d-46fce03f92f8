#!/bin/bash

# 代码质量Issue自动修复Agent启动脚本

echo "🤖 代码质量Issue自动修复Agent"
echo "================================"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

# 检查依赖
if [ ! -f "requirements.txt" ]; then
    echo "❌ requirements.txt 文件不存在"
    exit 1
fi

# 检查API密钥
if [ -z "$OPENAI_API_KEY" ]; then
    echo "⚠️  OPENAI_API_KEY 环境变量未设置"
    echo "请设置您的OpenAI API密钥:"
    echo "export OPENAI_API_KEY='your-api-key-here'"
    echo ""
    read -p "是否继续运行演示模式? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 安装依赖
echo "📦 检查依赖包..."
pip install -r requirements.txt > /dev/null 2>&1

# 创建必要的目录
mkdir -p demo_files test_files

# 显示菜单
echo ""
echo "请选择运行模式:"
echo "1) 运行功能演示 (demo.py)"
echo "2) 运行测试 (test_agent.py)"
echo "3) 处理所有文件 (main.py)"
echo "4) 可视化工作流 (visualize_graph.py)"
echo "5) 性能监控 (monitor.py)"
echo "6) 退出"
echo ""

read -p "请输入选择 (1-6): " choice

case $choice in
    1)
        echo "🎯 运行功能演示..."
        python3 demo.py
        ;;
    2)
        echo "🧪 运行测试..."
        python3 test_agent.py
        ;;
    3)
        echo "🔧 处理所有文件..."
        python3 main.py
        ;;
    4)
        echo "📊 生成工作流可视化..."
        python3 visualize_graph.py
        ;;
    5)
        echo "📈 性能监控..."
        python3 monitor.py
        ;;
    6)
        echo "👋 再见!"
        exit 0
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "✅ 执行完成!"

# 显示生成的文件
echo ""
echo "📁 生成的文件:"
if [ -f "autofix_argument.log" ]; then
    echo "  - autofix_argument.log (修复日志)"
fi
if [ -f "fix_results.json" ]; then
    echo "  - fix_results.json (修复结果)"
fi
if [ -f "agent_workflow.png" ]; then
    echo "  - agent_workflow.png (工作流程图)"
fi
if [ -f "agent_report.txt" ]; then
    echo "  - agent_report.txt (性能报告)"
fi
