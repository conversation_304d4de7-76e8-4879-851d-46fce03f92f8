#!/usr/bin/env python3
"""
配置文件
管理Agent的各种配置参数
"""

import os

# 基础路径配置
BASE_SOURCE_PATH = "/Users/<USER>/Workspace/git/ks-serveree-themis"

# 数据文件路径
ISSUES_FILE = "data/issues.json"
RULES_FILE = "data/rules_origin.json"
LOG_FILE = "autofix_argument.log"

# Agent配置
MAX_RETRY_COUNT = 2
ENABLE_VALIDATION = True

# 模型配置
DEFAULT_MODEL_NAME = "gpt-4o"
DEFAULT_TEMPERATURE = 0

def get_full_path(relative_path: str) -> str:
    """将相对路径转换为完整路径"""
    if os.path.isabs(relative_path):
        return relative_path
    return os.path.join(BASE_SOURCE_PATH, relative_path)

def validate_base_path():
    """验证基础路径是否存在"""
    if not os.path.exists(BASE_SOURCE_PATH):
        raise RuntimeError(f"Base source path does not exist: {BASE_SOURCE_PATH}")
    return True

def validate_data_files():
    """验证数据文件是否存在"""
    if not os.path.exists(ISSUES_FILE):
        raise RuntimeError(f"Issues file does not exist: {ISSUES_FILE}")
    
    if not os.path.exists(RULES_FILE):
        raise RuntimeError(f"Rules file does not exist: {RULES_FILE}")
    
    return True

def get_config_summary():
    """获取配置摘要"""
    return {
        "base_source_path": BASE_SOURCE_PATH,
        "issues_file": ISSUES_FILE,
        "rules_file": RULES_FILE,
        "log_file": LOG_FILE,
        "max_retry_count": MAX_RETRY_COUNT,
        "enable_validation": ENABLE_VALIDATION,
        "default_model": DEFAULT_MODEL_NAME,
        "default_temperature": DEFAULT_TEMPERATURE
    }
