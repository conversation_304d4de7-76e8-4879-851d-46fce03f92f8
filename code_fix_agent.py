"""
代码质量Issue自动修复Agent
基于LangGraph实现，包含工具、状态管理和反思链
"""

import json
import os
from typing import List, Dict, Any, Annotated
from typing_extensions import TypedDict
from pydantic import BaseModel, Field

from langchain_core.tools import tool
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, START, END, add_messages
from langgraph.prebuilt import ToolNode

from config import get_full_path, validate_base_path, validate_data_files


class CodeFixState(TypedDict):
    """Agent状态定义"""
    file_path: str
    issues: List[Dict[str, Any]]
    original_code: str
    rule_details: Dict[str, Any]
    fix_suggestions: List[Dict[str, Any]]
    validation_result: Dict[str, Any]
    messages: Annotated[List, add_messages]


class FileReadInput(BaseModel):
    """文件读取工具输入"""
    file_path: str = Field(description="要读取的文件路径")


class RuleQueryInput(BaseModel):
    """规则查询工具输入"""
    rule_key: str = Field(description="规则键名，如 'squid:S2111'")


class FileWriteInput(BaseModel):
    """文件写入工具输入"""
    file_path: str = Field(description="要写入的文件路径")
    content: str = Field(description="要写入的内容")


@tool(args_schema=FileReadInput)
def read_source_file(file_path: str) -> str:
    """根据指定路径读取文件源代码"""
    # 获取完整路径
    full_path = get_full_path(file_path)

    try:
        with open(full_path, 'r', encoding='utf-8') as f:
            content = f.read()
        return content
    except Exception as e:
        error_msg = f"CRITICAL ERROR: Failed to read file {full_path}: {str(e)}"
        print(error_msg)
        raise RuntimeError(error_msg)


@tool(args_schema=RuleQueryInput)
def get_rule_details(rule_key: str) -> str:
    """从rules_origin.json获取指定规则的详细介绍"""
    try:
        with open('data/rules_origin.json', 'r', encoding='utf-8') as f:
            rules = json.load(f)

        if rule_key in rules:
            rule_info = rules[rule_key]
            return json.dumps(rule_info, ensure_ascii=False, indent=2)
        else:
            error_msg = f"CRITICAL ERROR: Rule {rule_key} not found in rules database"
            print(error_msg)
            raise RuntimeError(error_msg)
    except FileNotFoundError:
        error_msg = "CRITICAL ERROR: rules_origin.json file not found"
        print(error_msg)
        raise RuntimeError(error_msg)
    except json.JSONDecodeError as e:
        error_msg = f"CRITICAL ERROR: Invalid JSON in rules_origin.json: {str(e)}"
        print(error_msg)
        raise RuntimeError(error_msg)
    except Exception as e:
        error_msg = f"CRITICAL ERROR: Failed to read rules: {str(e)}"
        print(error_msg)
        raise RuntimeError(error_msg)


@tool(args_schema=FileWriteInput)
def write_source_file(file_path: str, content: str) -> str:
    """将指定内容写入指定位置的文件"""
    # 获取完整路径
    full_path = get_full_path(file_path)

    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(full_path), exist_ok=True)

        with open(full_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return f"Successfully wrote content to {full_path}"
    except Exception as e:
        error_msg = f"CRITICAL ERROR: Failed to write file {full_path}: {str(e)}"
        print(error_msg)
        raise RuntimeError(error_msg)


# 工具列表
tools = [read_source_file, get_rule_details, write_source_file]
tool_node = ToolNode(tools)


class CodeFixAgent:
    """代码修复Agent主类"""

    def __init__(self, model_name: str = "gpt-4o"):
        # 验证基础路径和数据文件
        try:
            validate_base_path()
            validate_data_files()
        except RuntimeError as e:
            print(f"CRITICAL ERROR: Configuration validation failed: {e}")
            raise

        # 从环境变量读取配置
        api_key = os.getenv("OPENAI_API_KEY")
        base_url = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")

        if not api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")

        # 初始化模型，支持自定义endpoint
        self.model = ChatOpenAI(
            model=model_name,
            temperature=0,
            api_key=api_key,
            base_url=base_url
        )
        self.model_with_tools = self.model.bind_tools(tools)
        self.graph = self._build_graph()
    
    def _build_graph(self) -> StateGraph:
        """构建LangGraph工作流"""
        graph_builder = StateGraph(CodeFixState)
        
        # 添加节点
        graph_builder.add_node("analyze_issues", self._analyze_issues)
        graph_builder.add_node("generate_fixes", self._generate_fixes)
        graph_builder.add_node("validate_fixes", self._validate_fixes)
        graph_builder.add_node("apply_fixes", self._apply_fixes)
        graph_builder.add_node("tools", tool_node)
        
        # 添加边
        graph_builder.add_edge(START, "analyze_issues")
        graph_builder.add_conditional_edges(
            "analyze_issues",
            self._should_use_tools,
            {"tools": "tools", "continue": "generate_fixes"}
        )
        graph_builder.add_edge("tools", "generate_fixes")
        graph_builder.add_edge("generate_fixes", "validate_fixes")
        graph_builder.add_conditional_edges(
            "validate_fixes",
            self._should_retry,
            {"retry": "generate_fixes", "apply": "apply_fixes", "end": END}
        )
        graph_builder.add_edge("apply_fixes", END)
        
        return graph_builder.compile(
            checkpointer=None,
            interrupt_before=None,
            interrupt_after=None,
            debug=False
        )
    
    def _analyze_issues(self, state: CodeFixState) -> Dict[str, Any]:
        """分析issues并获取规则信息"""
        issues = state["issues"]
        file_path = state["file_path"]
        
        # 构建分析提示
        system_prompt = """你是一个代码质量专家。请分析给定的代码质量问题，并准备获取相关规则的详细信息。
        
        对于每个issue，你需要：
        1. 理解问题的性质
        2. 确定需要获取哪些规则的详细信息
        3. 准备读取源代码文件
        
        请使用工具来获取必要的信息。"""
        
        issues_text = json.dumps(issues, ensure_ascii=False, indent=2)
        
        human_message = f"""
        文件路径: {file_path}
        需要修复的Issues:
        {issues_text}
        
        请分析这些issues并获取相关规则信息和源代码。
        """
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=human_message)
        ]
        
        response = self.model_with_tools.invoke(messages)
        
        return {
            "messages": [response],
        }

    def _generate_fixes(self, state: CodeFixState) -> Dict[str, Any]:
        """生成代码修复方案"""
        issues = state["issues"]
        file_path = state["file_path"]

        # 更新重试计数
        retry_count = state.get("retry_count", 0)
        if retry_count is None:
            retry_count = 0

        # 从消息中提取工具调用结果
        messages = state.get("messages", [])

        system_prompt = """你是一个代码修复专家。基于提供的代码质量问题、规则详情和源代码，生成具体的修复方案。

        修复方案必须包含：
        1. 具体的行号
        2. 修改前的代码
        3. 修改后的代码

        输出格式必须是JSON数组，每个元素包含：
        {
            "line": 行号,
            "before": "修改前的代码",
            "after": "修改后的代码"
        }

        请确保修复方案：
        - 完全符合规则要求
        - 保持代码逻辑正确性
        - 最小化修改范围
        - 遵循最佳实践"""

        issues_text = json.dumps(issues, ensure_ascii=False, indent=2)

        human_message = f"""
        文件路径: {file_path}
        Issues: {issues_text}

        请基于已获取的规则信息和源代码，生成具体的修复方案。
        输出格式必须是有效的JSON数组。
        """

        # 构建完整的消息历史
        full_messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=human_message)
        ]

        # 添加之前的消息上下文
        if messages:
            full_messages = messages + [HumanMessage(content=human_message)]

        response = self.model.invoke(full_messages)

        # 尝试解析修复建议
        try:
            # 提取JSON内容
            content = response.content
            if "```json" in content:
                json_start = content.find("```json") + 7
                json_end = content.find("```", json_start)
                json_content = content[json_start:json_end].strip()
            elif "[" in content and "]" in content:
                json_start = content.find("[")
                json_end = content.rfind("]") + 1
                json_content = content[json_start:json_end]
            else:
                json_content = content

            fix_suggestions = json.loads(json_content)
        except Exception as e:
            fix_suggestions = []
            print(f"Error parsing fix suggestions: {e}")

        return {
            "fix_suggestions": fix_suggestions,
            "messages": messages + [response],
            "retry_count": retry_count + 1
        }

    def _validate_fixes(self, state: CodeFixState) -> Dict[str, Any]:
        """验证修复方案的正确性（反思链）"""
        fix_suggestions = state.get("fix_suggestions", [])
        issues = state["issues"]

        system_prompt = """你是一个代码审查专家。请仔细审查提供的修复方案，判断是否：

        1. 完全解决了原始问题
        2. 符合相关规则要求
        3. 保持代码逻辑正确性
        4. 没有引入新的问题
        5. 修改范围最小化

        如果发现问题，请详细说明并建议改进。

        输出格式：
        {
            "is_valid": true/false,
            "issues_found": ["问题1", "问题2", ...],
            "suggestions": ["建议1", "建议2", ...]
        }"""

        fix_text = json.dumps(fix_suggestions, ensure_ascii=False, indent=2)
        issues_text = json.dumps(issues, ensure_ascii=False, indent=2)

        human_message = f"""
        原始Issues: {issues_text}

        修复方案: {fix_text}

        请验证这些修复方案是否正确和完整。
        """

        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=human_message)
        ]

        response = self.model.invoke(messages)

        # 尝试解析验证结果
        try:
            content = response.content
            if "```json" in content:
                json_start = content.find("```json") + 7
                json_end = content.find("```", json_start)
                json_content = content[json_start:json_end].strip()
            elif "{" in content and "}" in content:
                json_start = content.find("{")
                json_end = content.rfind("}") + 1
                json_content = content[json_start:json_end]
            else:
                json_content = content

            validation_result = json.loads(json_content)
        except Exception as e:
            validation_result = {
                "is_valid": False,
                "issues_found": [f"解析验证结果失败: {e}"],
                "suggestions": ["请重新生成修复方案"]
            }

        return {
            "validation_result": validation_result,
            "messages": state.get("messages", []) + [response]
        }

    def _apply_fixes(self, state: CodeFixState) -> Dict[str, Any]:
        """应用修复到文件"""
        fix_suggestions = state.get("fix_suggestions", [])
        file_path = state["file_path"]
        original_code = state.get("original_code", "")

        # 获取完整路径
        full_path = get_full_path(file_path)

        if not fix_suggestions:
            return {"messages": [AIMessage(content="没有修复建议需要应用")]}

        # 读取原始代码（如果还没有读取）
        if not original_code:
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    original_code = f.read()
            except Exception as e:
                error_msg = f"CRITICAL ERROR: 无法读取文件 {full_path}: {e}"
                print(error_msg)
                raise RuntimeError(error_msg)

        # 应用修复
        lines = original_code.split('\n')

        # 按行号倒序排序，避免行号偏移问题
        sorted_fixes = sorted(fix_suggestions, key=lambda x: x.get("line", 0), reverse=True)

        for fix in sorted_fixes:
            line_num = fix.get("line", 0)
            before = fix.get("before", "")
            after = fix.get("after", "")

            if 1 <= line_num <= len(lines):
                # 验证原始代码是否匹配
                original_line = lines[line_num - 1]
                if before.strip() in original_line or original_line.strip() == before.strip():
                    lines[line_num - 1] = after
                else:
                    print(f"Warning: Line {line_num} doesn't match expected content")

        # 写入修复后的代码
        fixed_code = '\n'.join(lines)

        try:
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(fixed_code)

            # 记录修复日志
            self._log_fixes(file_path, fix_suggestions)

            return {
                "messages": [AIMessage(content=f"成功应用 {len(fix_suggestions)} 个修复到文件 {full_path}")]
            }
        except Exception as e:
            error_msg = f"CRITICAL ERROR: 应用修复失败: {e}"
            print(error_msg)
            raise RuntimeError(error_msg)

    def _log_fixes(self, file_path: str, fixes: List[Dict[str, Any]]):
        """记录修复日志到autofix_argument.log"""
        log_entry = {
            "file": file_path,
            "fixes": fixes,
            "timestamp": __import__('datetime').datetime.now().isoformat()
        }

        try:
            with open("autofix_argument.log", "a", encoding='utf-8') as f:
                f.write(json.dumps(log_entry, ensure_ascii=False) + "\n")
        except Exception as e:
            print(f"Failed to write log: {e}")

    def _should_use_tools(self, state: CodeFixState) -> str:
        """判断是否需要使用工具"""
        messages = state.get("messages", [])
        if messages and hasattr(messages[-1], 'tool_calls') and messages[-1].tool_calls:
            return "tools"
        return "continue"

    def _should_retry(self, state: CodeFixState) -> str:
        """判断是否需要重试修复"""
        validation_result = state.get("validation_result", {})
        retry_count = state.get("retry_count", 0)

        # 确保retry_count是数字
        if retry_count is None:
            retry_count = 0

        # 如果验证通过，直接应用
        if validation_result.get("is_valid", True):  # 默认为True，避免无限重试
            return "apply"

        # 如果重试次数超过限制，结束
        if retry_count >= 1:  # 减少重试次数，避免递归
            print(f"Warning: Max retry count reached ({retry_count}), skipping validation")
            return "apply"  # 强制应用，避免无限循环

        return "retry"

    def process_file_issues(self, file_path: str, issues: List[Dict[str, Any]]) -> Dict[str, Any]:
        """处理单个文件的issues"""
        initial_state = {
            "file_path": file_path,
            "issues": issues,
            "original_code": "",
            "rule_details": {},
            "fix_suggestions": [],
            "validation_result": {},
            "messages": [],
            "retry_count": 0
        }

        try:
            result = self.graph.invoke(initial_state)
            return result
        except Exception as e:
            error_msg = f"Agent processing failed: {str(e)}"
            print(f"CRITICAL ERROR: {error_msg}")
            return {
                "error": error_msg,
                "file_path": file_path,
                "issues": issues
            }
