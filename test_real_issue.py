#!/usr/bin/env python3
"""
测试Agent处理真实的代码质量问题
"""

import os
import json

# 设置环境变量
os.environ['OPENAI_API_KEY'] = 'sk-6dHyTrJ63OWstgZebpdAZSOhWEeBKvXEjGBMA5oblHZdIyLf'
os.environ['OPENAI_BASE_URL'] = 'https://www.dmxapi.com/v1'

def load_sample_issue():
    """加载一个示例issue"""
    try:
        with open('data/issues.json', 'r', encoding='utf-8') as f:
            issues_data = json.load(f)
        
        # 选择一个有问题的文件
        for file_path, issues in issues_data.items():
            if issues:  # 确保有问题
                return file_path, issues[0]  # 返回第一个问题
        
        return None, None
        
    except Exception as e:
        print(f"❌ 加载issues失败: {e}")
        return None, None

def test_agent_with_real_issue():
    """测试Agent处理真实问题"""
    print("🤖 测试Agent处理真实代码质量问题")
    print("=" * 50)
    
    # 1. 加载示例问题
    print("📋 加载示例问题...")
    file_path, issue = load_sample_issue()
    
    if not file_path or not issue:
        print("❌ 无法加载示例问题")
        return False
    
    print(f"📄 文件: {file_path}")
    print(f"🐛 规则: {issue.get('rule', 'Unknown')}")
    print(f"📍 行号: {issue.get('line', 'Unknown')}")
    print(f"💬 消息: {issue.get('message', 'No message')}")
    
    # 2. 初始化Agent
    print(f"\n🚀 初始化Agent...")
    try:
        from code_fix_agent import CodeFixAgent
        agent = CodeFixAgent()
        print("✅ Agent初始化成功")
    except Exception as e:
        print(f"❌ Agent初始化失败: {e}")
        return False
    
    # 3. 处理问题
    print(f"\n🔧 处理问题...")
    try:
        result = agent.process_file_issues(file_path, [issue])
        
        if "error" in result:
            print(f"❌ 处理失败: {result['error']}")
            return False
        
        # 4. 显示结果
        print(f"\n📊 处理结果:")
        fix_suggestions = result.get("fix_suggestions", [])
        validation_result = result.get("validation_result", {})
        
        print(f"🔧 修复建议数量: {len(fix_suggestions)}")
        print(f"✅ 验证结果: {validation_result.get('is_valid', 'Unknown')}")
        
        if fix_suggestions:
            print(f"\n📝 修复详情:")
            for i, fix in enumerate(fix_suggestions, 1):
                line = fix.get('line', 'Unknown')
                before = fix.get('before', 'N/A')
                after = fix.get('after', 'N/A')
                print(f"{i}. 行 {line}:")
                print(f"   修复前: {before}")
                print(f"   修复后: {after}")
        
        # 5. 检查日志
        if os.path.exists("autofix_argument.log"):
            print(f"\n📋 检查修复日志...")
            with open("autofix_argument.log", 'r', encoding='utf-8') as f:
                log_content = f.read()
            if log_content.strip():
                print("✅ 修复日志已生成")
            else:
                print("⚠️ 修复日志为空")
        
        print(f"\n🎉 测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_issues():
    """测试处理多个问题"""
    print(f"\n🔄 测试处理多个问题...")
    
    try:
        with open('data/issues.json', 'r', encoding='utf-8') as f:
            issues_data = json.load(f)
        
        # 找一个有多个问题的文件
        multi_issue_file = None
        multi_issues = []
        
        for file_path, issues in issues_data.items():
            if len(issues) > 1:
                multi_issue_file = file_path
                multi_issues = issues[:3]  # 只取前3个问题
                break
        
        if not multi_issue_file:
            print("⚠️ 没有找到有多个问题的文件")
            return True
        
        print(f"📄 文件: {multi_issue_file}")
        print(f"🐛 问题数量: {len(multi_issues)}")
        
        # 初始化Agent
        from code_fix_agent import CodeFixAgent
        agent = CodeFixAgent()
        
        # 处理多个问题
        result = agent.process_file_issues(multi_issue_file, multi_issues)
        
        if "error" in result:
            print(f"❌ 处理失败: {result['error']}")
            return False
        
        fix_suggestions = result.get("fix_suggestions", [])
        print(f"✅ 生成了 {len(fix_suggestions)} 个修复建议")
        
        return True
        
    except Exception as e:
        print(f"❌ 多问题测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 Agent真实问题处理测试")
    print("=" * 60)
    
    # 检查环境变量
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ OPENAI_API_KEY 未设置")
        return False
    
    print(f"✅ API配置: {os.getenv('OPENAI_BASE_URL', 'Default')}")
    
    try:
        # 测试单个问题
        success1 = test_agent_with_real_issue()
        
        # 测试多个问题
        success2 = test_multiple_issues()
        
        if success1 and success2:
            print(f"\n🎉 所有测试通过！Agent可以正常处理真实的代码质量问题。")
            return True
        else:
            print(f"\n⚠️ 部分测试失败。")
            return False
            
    except KeyboardInterrupt:
        print(f"\n👋 测试被用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
