"""
测试代码修复Agent的功能
"""

import os
import json
from code_fix_agent import CodeFixAgent


def test_single_issue():
    """测试单个issue的修复"""
    print("=== 测试单个Issue修复 ===")
    
    # 模拟一个简单的issue
    test_file_path = "test_files/TestBigDecimal.java"
    test_issues = [
        {
            "rule": "squid:S2111",
            "location": test_file_path,
            "line": 5,
            "textRange": {
                "startLine": 5,
                "endLine": 5,
                "startOffset": 32,
                "endOffset": 49
            },
            "message": "Use \"BigDecimal.valueOf\" instead."
        }
    ]
    
    # 创建测试文件
    os.makedirs("test_files", exist_ok=True)
    test_code = """public class TestBigDecimal {
    public static void main(String[] args) {
        double value = 1.1;
        // 这里有一个问题：使用了BigDecimal(double)构造函数
        BigDecimal bd = new BigDecimal(value);
        System.out.println(bd);
    }
}"""
    
    with open(test_file_path, 'w', encoding='utf-8') as f:
        f.write(test_code)
    
    print(f"Created test file: {test_file_path}")
    print("Original code:")
    print(test_code)
    print()
    
    # 初始化Agent
    agent = CodeFixAgent()
    
    # 处理issues
    result = agent.process_file_issues(test_file_path, test_issues)
    
    # 显示结果
    if "error" in result:
        print(f"❌ Error: {result['error']}")
    else:
        fix_suggestions = result.get("fix_suggestions", [])
        print(f"✅ Generated {len(fix_suggestions)} fixes:")
        
        for i, fix in enumerate(fix_suggestions, 1):
            print(f"\n{i}. Line {fix.get('line', 'Unknown')}:")
            print(f"   Before: {fix.get('before', 'N/A')}")
            print(f"   After:  {fix.get('after', 'N/A')}")
        
        # 显示修复后的代码
        try:
            with open(test_file_path, 'r', encoding='utf-8') as f:
                fixed_code = f.read()
            print(f"\nFixed code:")
            print(fixed_code)
        except Exception as e:
            print(f"Error reading fixed code: {e}")
    
    return result


def test_multiple_issues():
    """测试多个issues的修复"""
    print("\n=== 测试多个Issues修复 ===")
    
    test_file_path = "test_files/TestMultipleIssues.java"
    test_issues = [
        {
            "rule": "squid:S2111",
            "location": test_file_path,
            "line": 5,
            "textRange": {
                "startLine": 5,
                "endLine": 5,
                "startOffset": 32,
                "endOffset": 49
            },
            "message": "Use \"BigDecimal.valueOf\" instead."
        },
        {
            "rule": "squid:custom:LongVariableUseLowerCaseIChecker",
            "location": test_file_path,
            "line": 6,
            "textRange": {
                "startLine": 6,
                "endLine": 6,
                "startOffset": 17,
                "endOffset": 19
            },
            "message": "禁止使用小写字母l为long或Long类型变量赋值"
        }
    ]
    
    test_code = """public class TestMultipleIssues {
    public static void main(String[] args) {
        double value = 1.1;
        // Issue 1: BigDecimal构造函数问题
        BigDecimal bd = new BigDecimal(value);
        long count = 100l; // Issue 2: 小写l问题
        System.out.println(bd + ", " + count);
    }
}"""
    
    with open(test_file_path, 'w', encoding='utf-8') as f:
        f.write(test_code)
    
    print(f"Created test file: {test_file_path}")
    print("Original code:")
    print(test_code)
    print()
    
    # 初始化Agent
    agent = CodeFixAgent()
    
    # 处理issues
    result = agent.process_file_issues(test_file_path, test_issues)
    
    # 显示结果
    if "error" in result:
        print(f"❌ Error: {result['error']}")
    else:
        fix_suggestions = result.get("fix_suggestions", [])
        print(f"✅ Generated {len(fix_suggestions)} fixes:")
        
        for i, fix in enumerate(fix_suggestions, 1):
            print(f"\n{i}. Line {fix.get('line', 'Unknown')}:")
            print(f"   Before: {fix.get('before', 'N/A')}")
            print(f"   After:  {fix.get('after', 'N/A')}")
        
        # 显示修复后的代码
        try:
            with open(test_file_path, 'r', encoding='utf-8') as f:
                fixed_code = f.read()
            print(f"\nFixed code:")
            print(fixed_code)
        except Exception as e:
            print(f"Error reading fixed code: {e}")
    
    return result


def test_validation_failure():
    """测试验证失败的情况"""
    print("\n=== 测试验证失败情况 ===")
    
    # 这里可以模拟一个复杂的issue，让Agent生成可能不正确的修复方案
    # 然后验证反思链是否能够捕获问题
    
    test_file_path = "test_files/TestValidation.java"
    test_issues = [
        {
            "rule": "findbugs:WMI_WRONG_MAP_ITERATOR",
            "location": test_file_path,
            "line": 5,
            "textRange": {
                "startLine": 5,
                "endLine": 5,
                "startOffset": 0,
                "endOffset": 50
            },
            "message": "使用keySet迭代器而不是entrySet迭代器导致效率低下"
        }
    ]
    
    test_code = """public class TestValidation {
    public void processMap(Map<String, Integer> map) {
        for (String key : map.keySet()) {
            Integer value = map.get(key);
            System.out.println(key + ": " + value);
        }
    }
}"""
    
    with open(test_file_path, 'w', encoding='utf-8') as f:
        f.write(test_code)
    
    print(f"Created test file: {test_file_path}")
    print("Original code:")
    print(test_code)
    print()
    
    # 初始化Agent
    agent = CodeFixAgent()
    
    # 处理issues
    result = agent.process_file_issues(test_file_path, test_issues)
    
    # 显示结果
    validation_result = result.get("validation_result", {})
    print(f"Validation result: {validation_result}")
    
    if "error" in result:
        print(f"❌ Error: {result['error']}")
    else:
        fix_suggestions = result.get("fix_suggestions", [])
        print(f"✅ Generated {len(fix_suggestions)} fixes:")
        
        for i, fix in enumerate(fix_suggestions, 1):
            print(f"\n{i}. Line {fix.get('line', 'Unknown')}:")
            print(f"   Before: {fix.get('before', 'N/A')}")
            print(f"   After:  {fix.get('after', 'N/A')}")
    
    return result


def main():
    """运行所有测试"""
    print("代码修复Agent测试程序")
    print("=" * 50)
    
    # 检查环境变量
    if not os.getenv("OPENAI_API_KEY"):
        print("Error: OPENAI_API_KEY environment variable not set")
        print("Please set your OpenAI API key:")
        print("export OPENAI_API_KEY='your-api-key-here'")
        return
    
    try:
        # 运行测试
        test_single_issue()
        test_multiple_issues()
        test_validation_failure()
        
        print("\n" + "=" * 50)
        print("所有测试完成！")
        
        # 检查日志文件
        if os.path.exists("autofix_argument.log"):
            print("修复日志已保存到: autofix_argument.log")
            
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
