[{"rule": "squid:custom:AdFixedDomainChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java", "line": 49, "textRange": {"startLine": 49, "endLine": 49, "startOffset": 37, "endOffset": 94}, "message": "域名逃生不能硬编码CDN域名: kwai.com"}, {"rule": "squid:S2111", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 247, "textRange": {"startLine": 247, "endLine": 247, "startOffset": 32, "endOffset": 49}, "message": "Use \"BigDecimal.valueOf\" instead."}, {"rule": "squid:S2111", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 248, "textRange": {"startLine": 248, "endLine": 248, "startOffset": 33, "endOffset": 53}, "message": "Use \"BigDecimal.valueOf\" instead."}, {"rule": "findbugs:DMI_BIGDECIMAL_CONSTRUCTED_FROM_DOUBLE", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 249, "textRange": {"startLine": 249, "endLine": 249, "startOffset": 0, "endOffset": 53}, "message": "BigDecimal constructed from 3.8 in com.kuaishou.serveree.themis.api.ww.TestBigDecimal.test(float, double)"}, {"rule": "squid:S2111", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 249, "textRange": {"startLine": 249, "endLine": 249, "startOffset": 33, "endOffset": 52}, "message": "Use \"BigDecimal.valueOf\" instead."}, {"rule": "squid:S2111", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 251, "textRange": {"startLine": 251, "endLine": 251, "startOffset": 33, "endOffset": 54}, "message": "Use \"BigDecimal.valueOf\" instead."}, {"rule": "squid:custom:FloatingPointCalculationChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 251, "textRange": {"startLine": 251, "endLine": 251, "startOffset": 48, "endOffset": 53}, "message": "浮点数运算可能会有精度丢失问题, 建议改造为BigDecimal"}, {"rule": "findbugs:UUF_UNUSED_FIELD", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SqlRuleUtils.java", "line": 1, "textRange": {"startLine": 1, "endLine": 1, "startOffset": 0, "endOffset": 44}, "message": "Unused field: com.kuaishou.serveree.themis.api.ww.SqlRuleUtils$Interval.values"}, {"rule": "findbugs:UUF_UNUSED_FIELD", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SqlRuleUtils.java", "line": 1, "textRange": {"startLine": 1, "endLine": 1, "startOffset": 0, "endOffset": 44}, "message": "Unused field: com.kuaishou.serveree.themis.api.ww.BDCDD.values"}, {"rule": "findbugs:UUF_UNUSED_FIELD", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 1, "textRange": {"startLine": 1, "endLine": 1, "startOffset": 0, "endOffset": 44}, "message": "Unused field: com.kuaishou.serveree.themis.api.ww.A.age"}, {"rule": "squid:custom_KspayForbiddenBigDecimalEqualsMethodChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 255, "textRange": {"startLine": 255, "endLine": 255, "startOffset": 15, "endOffset": 26}, "message": "BigDecimal不允许使用equals方法比较，要用compare方法比较"}, {"rule": "squid:custom_KspayForbiddenBigDecimalEqualsMethodChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 259, "textRange": {"startLine": 259, "endLine": 259, "startOffset": 27, "endOffset": 38}, "message": "BigDecimal不允许使用equals方法比较，要用compare方法比较"}, {"rule": "squid:custom_KspayForbiddenBigDecimalEqualsMethodChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 260, "textRange": {"startLine": 260, "endLine": 260, "startOffset": 12, "endOffset": 23}, "message": "BigDecimal不允许使用equals方法比较，要用compare方法比较"}, {"rule": "squid:custom_KspayForbiddenBigDecimalEqualsMethodChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 263, "textRange": {"startLine": 263, "endLine": 263, "startOffset": 12, "endOffset": 37}, "message": "BigDecimal不允许使用equals方法比较，要用compare方法比较"}, {"rule": "squid:custom_KspayForbiddenBigDecimalEqualsMethodChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 266, "textRange": {"startLine": 266, "endLine": 266, "startOffset": 13, "endOffset": 38}, "message": "BigDecimal不允许使用equals方法比较，要用compare方法比较"}, {"rule": "squid:custom_KspayForbiddenBigDecimalEqualsMethodChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 269, "textRange": {"startLine": 269, "endLine": 269, "startOffset": 20, "endOffset": 45}, "message": "BigDecimal不允许使用equals方法比较，要用compare方法比较"}, {"rule": "squid:custom_KspayForbiddenBigDecimalEqualsMethodChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 270, "textRange": {"startLine": 270, "endLine": 270, "startOffset": 20, "endOffset": 45}, "message": "BigDecimal不允许使用equals方法比较，要用compare方法比较"}, {"rule": "findbugs:EQ_OVERRIDING_EQUALS_NOT_SYMMETRIC", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/Department.java", "line": 25, "textRange": {"startLine": 25, "endLine": 25, "startOffset": 0, "endOffset": 37}, "message": "com.kuaishou.serveree.themis.api.ww.BBB overrides equals in Department and may not be symmetric"}, {"rule": "findbugs:EQ_OVERRIDING_EQUALS_NOT_SYMMETRIC", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/Department.java", "line": 31, "textRange": {"startLine": 31, "endLine": 31, "startOffset": 0, "endOffset": 5}, "message": "com.kuaishou.serveree.themis.api.ww.CCC overrides equals in Department and may not be symmetric"}, {"rule": "findbugs:SIC_THREADLOCAL_DEADLY_EMBRACE", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 1, "textRange": {"startLine": 1, "endLine": 1, "startOffset": 0, "endOffset": 44}, "message": "com.kuaishou.serveree.themis.api.ww.ThreadLocalTest$1 needs to be _static_ to avoid a deadly embrace with com.kuaishou.serveree.themis.api.ww.ThreadLocalTest.redisData"}, {"rule": "findbugs:SIC_THREADLOCAL_DEADLY_EMBRACE", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 1, "textRange": {"startLine": 1, "endLine": 1, "startOffset": 0, "endOffset": 44}, "message": "com.kuaishou.serveree.themis.api.ww.ThreadLocalTest$1 needs to be _static_ to avoid a deadly embrace with com.kuaishou.serveree.themis.api.ww.ThreadLocalTest.kconfParameter"}, {"rule": "findbugs:SIC_THREADLOCAL_DEADLY_EMBRACE", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 1, "textRange": {"startLine": 1, "endLine": 1, "startOffset": 0, "endOffset": 44}, "message": "com.kuaishou.serveree.themis.api.ww.ThreadLocalTest$1 needs to be _static_ to avoid a deadly embrace with com.kuaishou.serveree.themis.api.ww.ThreadLocalTest.itemLabelScoreMap"}, {"rule": "squid:custom:FloatingPointCalculationChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 211, "textRange": {"startLine": 211, "endLine": 211, "startOffset": 18, "endOffset": 27}, "message": "浮点数运算可能会有精度丢失问题, 建议改造为BigDecimal"}, {"rule": "squid:custom:FloatingPointCalculationChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 214, "textRange": {"startLine": 214, "endLine": 214, "startOffset": 18, "endOffset": 27}, "message": "浮点数运算可能会有精度丢失问题, 建议改造为BigDecimal"}, {"rule": "squid:custom:BasicOrWrapperFloatCompareChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 211, "textRange": {"startLine": 211, "endLine": 211, "startOffset": 12, "endOffset": 28}, "message": "禁止用==比较浮点数大小"}, {"rule": "squid:custom:BasicOrWrapperFloatCompareChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 214, "textRange": {"startLine": 214, "endLine": 214, "startOffset": 12, "endOffset": 27}, "message": "禁止用==比较浮点数大小"}, {"rule": "squid:custom:BasicOrWrapperFloatCompareChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 217, "textRange": {"startLine": 217, "endLine": 217, "startOffset": 12, "endOffset": 33}, "message": "禁止用==比较浮点数大小"}, {"rule": "squid:custom:BasicOrWrapperFloatCompareChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 220, "textRange": {"startLine": 220, "endLine": 220, "startOffset": 12, "endOffset": 18}, "message": "禁止用==比较浮点数大小"}, {"rule": "squid:custom:BasicOrWrapperFloatCompareChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 223, "textRange": {"startLine": 223, "endLine": 223, "startOffset": 12, "endOffset": 21}, "message": "禁止用==比较浮点数大小"}, {"rule": "squid:custom:BasicOrWrapperFloatCompareChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 226, "textRange": {"startLine": 226, "endLine": 226, "startOffset": 12, "endOffset": 19}, "message": "禁止用==比较浮点数大小"}, {"rule": "squid:custom:BasicOrWrapperFloatCompareChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 229, "textRange": {"startLine": 229, "endLine": 229, "startOffset": 12, "endOffset": 21}, "message": "禁止用==比较浮点数大小"}, {"rule": "squid:custom:BasicOrWrapperFloatCompareChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 232, "textRange": {"startLine": 232, "endLine": 232, "startOffset": 12, "endOffset": 26}, "message": "浮点数的包装类型禁止使用equals进行比较"}, {"rule": "squid:custom:BasicOrWrapperFloatCompareChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 235, "textRange": {"startLine": 235, "endLine": 235, "startOffset": 12, "endOffset": 24}, "message": "浮点数的包装类型禁止使用equals进行比较"}, {"rule": "squid:custom:StaticThreadLocalAndScopeChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 275, "textRange": {"startLine": 275, "endLine": 275, "startOffset": 4, "endOffset": 92}, "message": "ThreadLocal和ScopeKey变量必须声明为static"}, {"rule": "squid:custom:StaticThreadLocalAndScopeChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 276, "textRange": {"startLine": 276, "endLine": 276, "startOffset": 4, "endOffset": 87}, "message": "ThreadLocal和ScopeKey变量必须声明为static"}, {"rule": "squid:custom:StaticThreadLocalAndScopeChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 284, "textRange": {"startLine": 277, "endLine": 284, "startOffset": 4, "endOffset": 16}, "message": "ThreadLocal和ScopeKey变量必须声明为static"}, {"rule": "findbugs:RV_RETURN_VALUE_IGNORED", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/PotentialFalsePositives.java", "line": 90, "textRange": {"startLine": 90, "endLine": 90, "startOffset": 0, "endOffset": 36}, "message": "Return value of String.trim() ignored in com.kuaishou.serveree.themis.api.ww.PotentialFalsePositives.unreachableNullBranch(String)"}, {"rule": "findbugs:RV_ABSOLUTE_VALUE_OF_HASHCODE", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/mr/rule/impl/MrPomSnapshotDependencyRule.java", "line": 73, "textRange": {"startLine": 73, "endLine": 73, "startOffset": 0, "endOffset": 85}, "message": "Bad attempt to compute absolute value of signed 32-bit hashcode in com.kuaishou.serveree.themis.component.service.mr.rule.impl.MrPomSnapshotDependencyRule.check(<PERSON><PERSON><PERSON><PERSON>pointBo, List)"}, {"rule": "findbugs:URF_UNREAD_FIELD", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SqlRuleUtils.java", "line": 1, "textRange": {"startLine": 1, "endLine": 1, "startOffset": 0, "endOffset": 44}, "message": "Unread field: com.kuaishou.serveree.themis.api.ww.BDCDD.upperInclusive"}, {"rule": "findbugs:URF_UNREAD_FIELD", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SqlRuleUtils.java", "line": 1, "textRange": {"startLine": 1, "endLine": 1, "startOffset": 0, "endOffset": 44}, "message": "Unread field: com.kuaishou.serveree.themis.api.ww.SqlRuleUtils$Interval.lowerInclusive"}, {"rule": "findbugs:URF_UNREAD_FIELD", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SqlRuleUtils.java", "line": 1, "textRange": {"startLine": 1, "endLine": 1, "startOffset": 0, "endOffset": 44}, "message": "Unread field: com.kuaishou.serveree.themis.api.ww.SqlRuleUtils$Interval.upperInclusive"}, {"rule": "findbugs:URF_UNREAD_FIELD", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SqlRuleUtils.java", "line": 1, "textRange": {"startLine": 1, "endLine": 1, "startOffset": 0, "endOffset": 44}, "message": "Unread field: com.kuaishou.serveree.themis.api.ww.BDCDD.lowerInclusive"}, {"rule": "findbugs:UR_UNINIT_READ", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SqlRuleUtils.java", "line": 31, "textRange": {"startLine": 31, "endLine": 31, "startOffset": 0, "endOffset": 41}, "message": "Uninitialized read of lowerBound in new com.kuaishou.serveree.themis.api.ww.SqlRuleUtils$Interval(String[])"}, {"rule": "findbugs:UR_UNINIT_READ", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SqlRuleUtils.java", "line": 32, "textRange": {"startLine": 32, "endLine": 32, "startOffset": 0, "endOffset": 41}, "message": "Uninitialized read of upperBound in new com.kuaishou.serveree.themis.api.ww.SqlRuleUtils$Interval(String[])"}, {"rule": "findbugs:UR_UNINIT_READ", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SqlRuleUtils.java", "line": 69, "textRange": {"startLine": 69, "endLine": 69, "startOffset": 0, "endOffset": 37}, "message": "Uninitialized read of lowerBound in new com.kuaishou.serveree.themis.api.ww.BDCDD(String[])"}, {"rule": "findbugs:SA_FIELD_SELF_ASSIGNMENT", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SqlRuleUtils.java", "line": 69, "textRange": {"startLine": 69, "endLine": 69, "startOffset": 0, "endOffset": 37}, "message": "Self assignment of field BDCDD.lowerBound in new com.kuaishou.serveree.themis.api.ww.BDCDD(String[])"}, {"rule": "findbugs:UR_UNINIT_READ", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SqlRuleUtils.java", "line": 70, "textRange": {"startLine": 70, "endLine": 70, "startOffset": 0, "endOffset": 37}, "message": "Uninitialized read of upperBound in new com.kuaishou.serveree.themis.api.ww.BDCDD(String[])"}, {"rule": "findbugs:SA_FIELD_SELF_ASSIGNMENT", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SqlRuleUtils.java", "line": 70, "textRange": {"startLine": 70, "endLine": 70, "startOffset": 0, "endOffset": 37}, "message": "Self assignment of field BDCDD.upperBound in new com.kuaishou.serveree.themis.api.ww.BDCDD(String[])"}, {"rule": "findbugs:SA_FIELD_SELF_ASSIGNMENT", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SqlRuleUtils.java", "line": 71, "textRange": {"startLine": 71, "endLine": 71, "startOffset": 0, "endOffset": 45}, "message": "Self assignment of field BDCDD.lowerInclusive in new com.kuaishou.serveree.themis.api.ww.BDCDD(String[])"}, {"rule": "findbugs:SA_FIELD_SELF_ASSIGNMENT", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SqlRuleUtils.java", "line": 72, "textRange": {"startLine": 72, "endLine": 72, "startOffset": 0, "endOffset": 45}, "message": "Self assignment of field BDCDD.upperInclusive in new com.kuaishou.serveree.themis.api.ww.BDCDD(String[])"}, {"rule": "squid:S2068", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/mybatis/MybatisPlusGenerator.java", "line": 41, "textRange": {"startLine": 41, "endLine": 41, "startOffset": 32, "endOffset": 52}, "message": "'PASSWORD' detected in this expression, review this potentially hard-coded credential."}, {"rule": "findbugs:BC_IMPOSSIBLE_DOWNCAST_OF_TOARRAY", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 107, "textRange": {"startLine": 107, "endLine": 107, "startOffset": 0, "endOffset": 60}, "message": "Impossible downcast of toArray() result to String[] in com.kuaishou.serveree.themis.api.ww.ToArrayChec1111k$MySet.callToArray()"}, {"rule": "findbugs:BC_IMPOSSIBLE_DOWNCAST_OF_TOARRAY", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 93, "textRange": {"startLine": 93, "endLine": 93, "startOffset": 0, "endOffset": 72}, "message": "Impossible downcast of toArray() result to String[] in com.kuaishou.serveree.themis.api.ww.ToArrayChec1111k.foo(List, List, Set, Collection)"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/utils/IssueUtils.java", "line": 530, "textRange": {"startLine": 527, "endLine": 530, "startOffset": 25, "endOffset": 17}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S2068", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/datasource/ThemisDataSourceMapSetter.java", "line": 56, "textRange": {"startLine": 56, "endLine": 56, "startOffset": 32, "endOffset": 52}, "message": "'PASSWORD' detected in this expression, review this potentially hard-coded credential."}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/sonar/mode/ScanModeService.java", "line": 63, "textRange": {"startLine": 63, "endLine": 63, "startOffset": 75, "endOffset": 131}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:custom:SelectSqlNoLimitChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/common/mappers/CheckRuleMapper.java", "line": 24, "textRange": {"startLine": 24, "endLine": 24, "startOffset": 12, "endOffset": 80}, "message": "Select语句必须加上limit字段"}, {"rule": "findbugs:RCN_REDUNDANT_NULLCHECK_WOULD_HAVE_BEEN_A_NPE", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/client/compile/CompileApi.java", "line": 97, "textRange": {"startLine": 97, "endLine": 97, "startOffset": 0, "endOffset": 35}, "message": "Nullcheck of response at line 97 of value previously dereferenced in com.kuaishou.serveree.themis.component.client.compile.CompileApi.checkSnapshotDependency(Integer, String, String, String)"}, {"rule": "findbugs:SA_FIELD_SELF_ASSIGNMENT", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SqlRuleUtils.java", "line": 31, "textRange": {"startLine": 31, "endLine": 31, "startOffset": 0, "endOffset": 41}, "message": "Self assignment of field SqlRuleUtils$Interval.lowerBound in new com.kuaishou.serveree.themis.api.ww.SqlRuleUtils$Interval(String[])"}, {"rule": "findbugs:SA_FIELD_SELF_ASSIGNMENT", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SqlRuleUtils.java", "line": 32, "textRange": {"startLine": 32, "endLine": 32, "startOffset": 0, "endOffset": 41}, "message": "Self assignment of field SqlRuleUtils$Interval.upperBound in new com.kuaishou.serveree.themis.api.ww.SqlRuleUtils$Interval(String[])"}, {"rule": "findbugs:SA_FIELD_SELF_ASSIGNMENT", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SqlRuleUtils.java", "line": 33, "textRange": {"startLine": 33, "endLine": 33, "startOffset": 0, "endOffset": 49}, "message": "Self assignment of field SqlRuleUtils$Interval.lowerInclusive in new com.kuaishou.serveree.themis.api.ww.SqlRuleUtils$Interval(String[])"}, {"rule": "findbugs:SA_FIELD_SELF_ASSIGNMENT", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SqlRuleUtils.java", "line": 34, "textRange": {"startLine": 34, "endLine": 34, "startOffset": 0, "endOffset": 49}, "message": "Self assignment of field SqlRuleUtils$Interval.upperInclusive in new com.kuaishou.serveree.themis.api.ww.SqlRuleUtils$Interval(String[])"}, {"rule": "findbugs:WMI_WRONG_MAP_ITERATOR", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/LocationEntity.java", "line": 185, "textRange": {"startLine": 185, "endLine": 185, "startOffset": 0, "endOffset": 76}, "message": "com.kuaishou.serveree.themis.api.ww.LocationEntity$ProvinceCityMapping.<static initializer for ProvinceCityMapping>() makes inefficient use of keySet iterator instead of entrySet iterator"}, {"rule": "squid:custom:LogParameterUseStringConcatChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/CheckProfileServiceImpl.java", "line": 200, "textRange": {"startLine": 200, "endLine": 200, "startOffset": 16, "endOffset": 103}, "message": "禁止使用字符串拼接构造日志内容"}, {"rule": "squid:custom:LogParameterUseStringConcatChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/platform/notice/PlatformKimMsgSendService.java", "line": 105, "textRange": {"startLine": 105, "endLine": 105, "startOffset": 12, "endOffset": 64}, "message": "禁止使用字符串拼接构造日志内容"}, {"rule": "squid:custom:LogParameterUseStringConcatChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/platform/notice/PlatformKimMsgSendService.java", "line": 68, "textRange": {"startLine": 68, "endLine": 68, "startOffset": 12, "endOffset": 64}, "message": "禁止使用字符串拼接构造日志内容"}, {"rule": "squid:custom:LongVariableUseLowerCaseIChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 113, "textRange": {"startLine": 113, "endLine": 113, "startOffset": 26, "endOffset": 28}, "message": "禁止使用小写字母l为long或Long类型变量赋值"}, {"rule": "squid:custom:LongVariableUseLowerCaseIChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 116, "textRange": {"startLine": 116, "endLine": 116, "startOffset": 13, "endOffset": 17}, "message": "禁止使用小写字母l为long或Long类型变量赋值"}, {"rule": "squid:custom:LongVariableUseLowerCaseIChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 120, "textRange": {"startLine": 120, "endLine": 120, "startOffset": 17, "endOffset": 21}, "message": "禁止使用小写字母l为long或Long类型变量赋值"}, {"rule": "squid:custom:LongVariableUseLowerCaseIChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 122, "textRange": {"startLine": 122, "endLine": 122, "startOffset": 17, "endOffset": 21}, "message": "禁止使用小写字母l为long或Long类型变量赋值"}, {"rule": "squid:custom:LongVariableUseLowerCaseIChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 126, "textRange": {"startLine": 126, "endLine": 126, "startOffset": 12, "endOffset": 16}, "message": "禁止使用小写字母l为long或Long类型变量赋值"}, {"rule": "squid:custom:LongVariableUseLowerCaseIChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 127, "textRange": {"startLine": 127, "endLine": 127, "startOffset": 12, "endOffset": 16}, "message": "禁止使用小写字母l为long或Long类型变量赋值"}, {"rule": "squid:custom:CatchAndIgnoreExceptionChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 142, "textRange": {"startLine": 142, "endLine": 142, "startOffset": 10, "endOffset": 31}, "message": "捕获并被忽略的异常，要注释写明原因"}, {"rule": "squid:custom:CollectionToArrayParameterChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 90, "textRange": {"startLine": 90, "endLine": 90, "startOffset": 27, "endOffset": 66}, "message": "集合转数组时请使用toArray(new T[0])或toArray(T[]::new)"}, {"rule": "squid:custom:CollectionToArrayParameterChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 91, "textRange": {"startLine": 91, "endLine": 91, "startOffset": 23, "endOffset": 58}, "message": "集合转数组时请使用toArray(new T[0])或toArray(T[]::new)"}, {"rule": "squid:custom:CollectionToArrayParameterChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 92, "textRange": {"startLine": 92, "endLine": 92, "startOffset": 23, "endOffset": 76}, "message": "集合转数组时请使用toArray(new T[0])或toArray(T[]::new)"}, {"rule": "squid:custom:CollectionToArrayParameterChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 93, "textRange": {"startLine": 93, "endLine": 93, "startOffset": 33, "endOffset": 55}, "message": "集合转数组时请使用toArray(new T[0])或toArray(T[]::new)"}, {"rule": "findbugs:NP_ALWAYS_NULL", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 167, "textRange": {"startLine": 167, "endLine": 167, "startOffset": 0, "endOffset": 124}, "message": "Null pointer dereference of oldRights in com.kuaishou.serveree.themis.api.ww.NpeTest.test1(RepoSettingResponse)"}, {"rule": "squid:custom:LockInTryBlockChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 37, "textRange": {"startLine": 37, "endLine": 37, "startOffset": 8, "endOffset": 19}, "message": "禁止在try块内加锁，加锁后必须使用try...finally释放"}, {"rule": "squid:custom:LockInTryBlockChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 40, "textRange": {"startLine": 39, "endLine": 40, "startOffset": 8, "endOffset": 23}, "message": "禁止在try块内加锁，加锁后必须使用try...finally释放"}, {"rule": "squid:custom:LockInTryBlockChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 42, "textRange": {"startLine": 41, "endLine": 42, "startOffset": 12, "endOffset": 27}, "message": "禁止在try块内加锁，加锁后必须使用try...finally释放"}, {"rule": "squid:custom:LockInTryBlockChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 55, "textRange": {"startLine": 49, "endLine": 55, "startOffset": 8, "endOffset": 23}, "message": "禁止在try块内加锁，加锁后必须使用try...finally释放"}, {"rule": "squid:custom:LockInTryBlockChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 51, "textRange": {"startLine": 50, "endLine": 51, "startOffset": 12, "endOffset": 27}, "message": "禁止在try块内加锁，加锁后必须使用try...finally释放"}, {"rule": "squid:custom:LockInTryBlockChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 65, "textRange": {"startLine": 59, "endLine": 65, "startOffset": 8, "endOffset": 23}, "message": "禁止在try块内加锁，加锁后必须使用try...finally释放"}, {"rule": "squid:custom:LockInTryBlockChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 61, "textRange": {"startLine": 60, "endLine": 61, "startOffset": 12, "endOffset": 27}, "message": "禁止在try块内加锁，加锁后必须使用try...finally释放"}, {"rule": "squid:custom:LockInTryBlockChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 69, "textRange": {"startLine": 69, "endLine": 69, "startOffset": 8, "endOffset": 19}, "message": "禁止在try块内加锁，加锁后必须使用try...finally释放"}, {"rule": "squid:custom:FloatingPointCalculationChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/entity/platform/FileState.java", "line": 50, "textRange": {"startLine": 50, "endLine": 50, "startOffset": 41, "endOffset": 86}, "message": "浮点数运算可能会有精度丢失问题, 建议改造为BigDecimal"}, {"rule": "squid:custom:SelectSqlNoLimitChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/common/mappers/ComplexityRepositoryMapper.java", "line": 24, "textRange": {"startLine": 24, "endLine": 24, "startOffset": 12, "endOffset": 89}, "message": "Select语句必须加上limit字段"}, {"rule": "squid:custom:CaseAddBreakChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/process/impl/ProcessCheckServiceImpl.java", "line": 281, "textRange": {"startLine": 280, "endLine": 281, "startOffset": 12, "endOffset": 108}, "message": "switch break检查"}, {"rule": "squid:custom:EnumGetNameChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/constant/analyze/LocalAnalyzeTaskEnum.java", "line": 38, "textRange": {"startLine": 38, "endLine": 38, "startOffset": 12, "endOffset": 18}, "message": "Enum里面成员变量名不要使用name,这样对应的getName和枚举的name方法有可能被混用"}, {"rule": "findbugs:RV_ABSOLUTE_VALUE_OF_HASHCODE", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/mr/rule/impl/MrReleaseRootPomVersionRule.java", "line": 75, "textRange": {"startLine": 75, "endLine": 75, "startOffset": 0, "endOffset": 81}, "message": "Bad attempt to compute absolute value of signed 32-bit hashcode in com.kuaishou.serveree.themis.component.service.mr.rule.impl.MrReleaseRootPomVersionRule.check(Mr<PERSON><PERSON><PERSON>pointBo, List)"}, {"rule": "squid:custom:FloatingPointCalculationChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/platform/skyeye/impl/FolderMaintainabilityServiceImpl.java", "line": 74, "textRange": {"startLine": 73, "endLine": 74, "startOffset": 41, "endOffset": 70}, "message": "浮点数运算可能会有精度丢失问题, 建议改造为BigDecimal"}, {"rule": "squid:custom:FloatingPointCalculationChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/platform/skyeye/impl/FolderMaintainabilityServiceImpl.java", "line": 76, "textRange": {"startLine": 76, "endLine": 76, "startOffset": 60, "endOffset": 106}, "message": "浮点数运算可能会有精度丢失问题, 建议改造为BigDecimal"}, {"rule": "squid:custom:CountDownLatch<PERSON>wait<PERSON>hecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/check/QualityTaskKspSyncService.java", "line": 162, "textRange": {"startLine": 162, "endLine": 162, "startOffset": 12, "endOffset": 34}, "message": "CountDownLatch.await里面最好加上超时时间，防止阻塞"}, {"rule": "squid:custom:MainSiteIpV6ForbiddenChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/platform/impl/CoverityServiceImpl.java", "line": 151, "textRange": {"startLine": 151, "endLine": 151, "startOffset": 12, "endOffset": 44}, "message": "调用了禁止使用的获取ip方法，请修改"}, {"rule": "squid:custom:SelectSqlNoLimitChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/common/mappers/PCheckIssueMapper.java", "line": 33, "textRange": {"startLine": 33, "endLine": 33, "startOffset": 12, "endOffset": 129}, "message": "Select语句必须加上limit字段"}, {"rule": "findbugs:SIC_INNER_SHOULD_BE_STATIC", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/CustomRuleDemoV1.java", "line": 15, "textRange": {"startLine": 15, "endLine": 15, "startOffset": 0, "endOffset": 9}, "message": "Should com.kuaishou.serveree.themis.api.ww.CustomRuleDemoV1$ExpressStateIcon be a _static_ inner class?"}, {"rule": "findbugs:RV_ABSOLUTE_VALUE_OF_HASHCODE", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/mr/rule/impl/MrLogbackPackagingDataRule.java", "line": 118, "textRange": {"startLine": 118, "endLine": 118, "startOffset": 0, "endOffset": 85}, "message": "Bad attempt to compute absolute value of signed 32-bit hashcode in com.kuaishou.serveree.themis.component.service.mr.rule.impl.MrLogbackPackagingDataRule.check(Mr<PERSON><PERSON><PERSON>pointBo, List)"}, {"rule": "squid:custom:KspayAbsoluteValueCalculationChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/proxy/algorithm/ConsistentHashingWithVirtualNode.java", "line": 63, "textRange": {"startLine": 63, "endLine": 63, "startOffset": 19, "endOffset": 37}, "message": "分片结果可能为负值，应取绝对值来进行后续计算操作"}, {"rule": "squid:custom:KspayAbsoluteValueCalculationChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/proxy/algorithm/ConsistentHashingWithoutVirtualNode.java", "line": 61, "textRange": {"startLine": 61, "endLine": 61, "startOffset": 15, "endOffset": 33}, "message": "分片结果可能为负值，应取绝对值来进行后续计算操作"}, {"rule": "squid:custom:SelectSqlNoLimitChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/common/mappers/IssueSummaryBaseMapper.java", "line": 23, "textRange": {"startLine": 23, "endLine": 23, "startOffset": 12, "endOffset": 99}, "message": "Select语句必须加上limit字段"}, {"rule": "squid:custom:SelectSqlNoLimitChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/common/mappers/ComplexityFileMapper.java", "line": 24, "textRange": {"startLine": 24, "endLine": 24, "startOffset": 12, "endOffset": 82}, "message": "Select语句必须加上limit字段"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/IssueSummaryServiceImpl.java", "line": 288, "textRange": {"startLine": 288, "endLine": 288, "startOffset": 25, "endOffset": 82}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:custom:CountDownLatch<PERSON>wait<PERSON>hecker", "location": "ks-serveree-themis-runner/src/main/java/com/kuaishou/serveree/themis/runner/task/KspProductAddLabelTask.java", "line": 82, "textRange": {"startLine": 82, "endLine": 82, "startOffset": 12, "endOffset": 34}, "message": "CountDownLatch.await里面最好加上超时时间，防止阻塞"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/platform/impl/PlatformRuleServiceImpl.java", "line": 405, "textRange": {"startLine": 405, "endLine": 405, "startOffset": 16, "endOffset": 113}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:custom:EnumGetNameChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/constant/platform/PlatformLanguageEnum.java", "line": 45, "textRange": {"startLine": 45, "endLine": 45, "startOffset": 18, "endOffset": 24}, "message": "Enum里面成员变量名不要使用name,这样对应的getName和枚举的name方法有可能被混用"}, {"rule": "findbugs:DMI_EMPTY_DB_PASSWORD", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/utils/HiveJdbcUtil.java", "line": 86, "textRange": {"startLine": 86, "endLine": 86, "startOffset": 0, "endOffset": 91}, "message": "Empty database password in com.kuaishou.serveree.themis.component.utils.HiveJdbcUtil.getConnection(long)"}, {"rule": "squid:custom:KspayAbsoluteValueCalculationChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/proxy/algorithm/CodeScanningHashUtil.java", "line": 54, "textRange": {"startLine": 54, "endLine": 54, "startOffset": 19, "endOffset": 45}, "message": "分片结果可能为负值，应取绝对值来进行后续计算操作"}, {"rule": "squid:custom:SelectSqlNoLimitChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/common/mappers/ThemisAnalyzeIssueMapper.java", "line": 26, "textRange": {"startLine": 25, "endLine": 26, "startOffset": 12, "endOffset": 70}, "message": "Select语句必须加上limit字段"}, {"rule": "squid:custom:CountDownLatch<PERSON>wait<PERSON>hecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/statics/impl/LocalStringMatchScanServiceImpl.java", "line": 183, "textRange": {"startLine": 183, "endLine": 183, "startOffset": 16, "endOffset": 38}, "message": "CountDownLatch.await里面最好加上超时时间，防止阻塞"}, {"rule": "squid:custom:SelectSqlNoLimitChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/common/mappers/IssueSummaryMapper.java", "line": 58, "textRange": {"startLine": 58, "endLine": 58, "startOffset": 12, "endOffset": 65}, "message": "Select语句必须加上limit字段"}, {"rule": "squid:custom:CatchAndIgnoreExceptionChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/utils/HiveJdbcUtil.java", "line": 57, "textRange": {"startLine": 57, "endLine": 57, "startOffset": 10, "endOffset": 34}, "message": "捕获并被忽略的异常，要注释写明原因"}, {"rule": "squid:custom:ThreadPoolThreadNameParameterChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/statics/az2/impl/DefaultAZ2HardCodeStringMatchScanHelper.java", "line": 87, "textRange": {"startLine": 87, "endLine": 87, "startOffset": 12, "endOffset": 42}, "message": "创建线程池时，必须提供有意义的线程命名参数"}, {"rule": "squid:custom:ThreadPoolCreateMethodChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/statics/az2/impl/DefaultAZ2HardCodeStringMatchScanHelper.java", "line": 87, "textRange": {"startLine": 87, "endLine": 87, "startOffset": 12, "endOffset": 42}, "message": "请使用基础架构封装的ExecutorEx和DynamicExecutor类创建线程池"}, {"rule": "squid:custom:ThreadPoolCreateMethodChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/ExecutorConfig.java", "line": 101, "textRange": {"startLine": 101, "endLine": 101, "startOffset": 15, "endOffset": 38}, "message": "请使用基础架构封装的ExecutorEx和DynamicExecutor类创建线程池"}, {"rule": "squid:custom:ThreadPoolCreateMethodChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/ExecutorConfig.java", "line": 113, "textRange": {"startLine": 113, "endLine": 113, "startOffset": 15, "endOffset": 38}, "message": "请使用基础架构封装的ExecutorEx和DynamicExecutor类创建线程池"}, {"rule": "squid:custom:ThreadPoolCreateMethodChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/ExecutorConfig.java", "line": 125, "textRange": {"startLine": 125, "endLine": 125, "startOffset": 15, "endOffset": 38}, "message": "请使用基础架构封装的ExecutorEx和DynamicExecutor类创建线程池"}, {"rule": "squid:custom:ThreadPoolCreateMethodChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/ExecutorConfig.java", "line": 137, "textRange": {"startLine": 137, "endLine": 137, "startOffset": 15, "endOffset": 38}, "message": "请使用基础架构封装的ExecutorEx和DynamicExecutor类创建线程池"}, {"rule": "squid:custom:ThreadPoolCreateMethodChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/ExecutorConfig.java", "line": 149, "textRange": {"startLine": 149, "endLine": 149, "startOffset": 15, "endOffset": 38}, "message": "请使用基础架构封装的ExecutorEx和DynamicExecutor类创建线程池"}, {"rule": "squid:custom:ThreadPoolCreateMethodChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/ExecutorConfig.java", "line": 161, "textRange": {"startLine": 161, "endLine": 161, "startOffset": 15, "endOffset": 38}, "message": "请使用基础架构封装的ExecutorEx和DynamicExecutor类创建线程池"}, {"rule": "squid:custom:ThreadPoolCreateMethodChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/ExecutorConfig.java", "line": 173, "textRange": {"startLine": 173, "endLine": 173, "startOffset": 15, "endOffset": 38}, "message": "请使用基础架构封装的ExecutorEx和DynamicExecutor类创建线程池"}, {"rule": "squid:custom:ThreadPoolCreateMethodChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/ExecutorConfig.java", "line": 185, "textRange": {"startLine": 185, "endLine": 185, "startOffset": 15, "endOffset": 38}, "message": "请使用基础架构封装的ExecutorEx和DynamicExecutor类创建线程池"}, {"rule": "squid:custom:ThreadPoolCreateMethodChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/ExecutorConfig.java", "line": 197, "textRange": {"startLine": 197, "endLine": 197, "startOffset": 15, "endOffset": 38}, "message": "请使用基础架构封装的ExecutorEx和DynamicExecutor类创建线程池"}, {"rule": "squid:custom:ThreadPoolCreateMethodChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/ExecutorConfig.java", "line": 209, "textRange": {"startLine": 209, "endLine": 209, "startOffset": 15, "endOffset": 38}, "message": "请使用基础架构封装的ExecutorEx和DynamicExecutor类创建线程池"}, {"rule": "squid:custom:ThreadPoolCreateMethodChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/ExecutorConfig.java", "line": 230, "textRange": {"startLine": 230, "endLine": 230, "startOffset": 15, "endOffset": 38}, "message": "请使用基础架构封装的ExecutorEx和DynamicExecutor类创建线程池"}, {"rule": "squid:custom:ThreadPoolCreateMethodChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/ExecutorConfig.java", "line": 242, "textRange": {"startLine": 242, "endLine": 242, "startOffset": 15, "endOffset": 38}, "message": "请使用基础架构封装的ExecutorEx和DynamicExecutor类创建线程池"}, {"rule": "squid:custom:ThreadPoolCreateMethodChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/ExecutorConfig.java", "line": 254, "textRange": {"startLine": 254, "endLine": 254, "startOffset": 15, "endOffset": 38}, "message": "请使用基础架构封装的ExecutorEx和DynamicExecutor类创建线程池"}, {"rule": "squid:custom:ThreadPoolCreateMethodChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/ExecutorConfig.java", "line": 266, "textRange": {"startLine": 266, "endLine": 266, "startOffset": 15, "endOffset": 38}, "message": "请使用基础架构封装的ExecutorEx和DynamicExecutor类创建线程池"}, {"rule": "squid:custom:ThreadPoolCreateMethodChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/ExecutorConfig.java", "line": 278, "textRange": {"startLine": 278, "endLine": 278, "startOffset": 15, "endOffset": 38}, "message": "请使用基础架构封装的ExecutorEx和DynamicExecutor类创建线程池"}, {"rule": "squid:custom:ThreadPoolCreateMethodChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/ExecutorConfig.java", "line": 290, "textRange": {"startLine": 290, "endLine": 290, "startOffset": 15, "endOffset": 38}, "message": "请使用基础架构封装的ExecutorEx和DynamicExecutor类创建线程池"}, {"rule": "squid:custom:ThreadPoolCreateMethodChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/ExecutorConfig.java", "line": 302, "textRange": {"startLine": 302, "endLine": 302, "startOffset": 15, "endOffset": 38}, "message": "请使用基础架构封装的ExecutorEx和DynamicExecutor类创建线程池"}, {"rule": "squid:custom:ThreadPoolCreateMethodChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/ExecutorConfig.java", "line": 314, "textRange": {"startLine": 314, "endLine": 314, "startOffset": 15, "endOffset": 38}, "message": "请使用基础架构封装的ExecutorEx和DynamicExecutor类创建线程池"}, {"rule": "squid:custom:ThreadPoolCreateMethodChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/ExecutorConfig.java", "line": 41, "textRange": {"startLine": 41, "endLine": 41, "startOffset": 15, "endOffset": 38}, "message": "请使用基础架构封装的ExecutorEx和DynamicExecutor类创建线程池"}, {"rule": "squid:custom:ThreadPoolCreateMethodChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/ExecutorConfig.java", "line": 53, "textRange": {"startLine": 53, "endLine": 53, "startOffset": 15, "endOffset": 38}, "message": "请使用基础架构封装的ExecutorEx和DynamicExecutor类创建线程池"}, {"rule": "squid:custom:ThreadPoolCreateMethodChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/ExecutorConfig.java", "line": 65, "textRange": {"startLine": 65, "endLine": 65, "startOffset": 15, "endOffset": 38}, "message": "请使用基础架构封装的ExecutorEx和DynamicExecutor类创建线程池"}, {"rule": "squid:custom:ThreadPoolCreateMethodChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/ExecutorConfig.java", "line": 77, "textRange": {"startLine": 77, "endLine": 77, "startOffset": 15, "endOffset": 38}, "message": "请使用基础架构封装的ExecutorEx和DynamicExecutor类创建线程池"}, {"rule": "squid:custom:ThreadPoolCreateMethodChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/ExecutorConfig.java", "line": 89, "textRange": {"startLine": 89, "endLine": 89, "startOffset": 15, "endOffset": 38}, "message": "请使用基础架构封装的ExecutorEx和DynamicExecutor类创建线程池"}, {"rule": "squid:custom:ManualCreateThreadChecker", "location": "ks-serveree-themis-runner/src/main/java/com/kuaishou/serveree/themis/runner/kafka/platform/PlatformMethodComplexityConsumer.java", "line": 117, "textRange": {"startLine": 111, "endLine": 117, "startOffset": 16, "endOffset": 17}, "message": "禁止在应用中自行显式创建线程"}, {"rule": "squid:custom:ManualCreateThreadChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/RefreshableLocalCache.java", "line": 63, "textRange": {"startLine": 63, "endLine": 63, "startOffset": 25, "endOffset": 50}, "message": "禁止在应用中自行显式创建线程"}, {"rule": "squid:custom:LogParameterUseStringConcatChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/kdev/impl/MrCheckpointCodeScanService.java", "line": 538, "textRange": {"startLine": 538, "endLine": 538, "startOffset": 12, "endOffset": 86}, "message": "禁止使用字符串拼接构造日志内容"}, {"rule": "squid:custom:CollectionToArrayParameterChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/entity/issue/StateMachine.java", "line": 30, "textRange": {"startLine": 30, "endLine": 30, "startOffset": 46, "endOffset": 107}, "message": "集合转数组时请使用toArray(new T[0])或toArray(T[]::new)"}, {"rule": "squid:custom:CollectionToArrayParameterChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/entity/issue/Transition.java", "line": 29, "textRange": {"startLine": 29, "endLine": 29, "startOffset": 21, "endOffset": 89}, "message": "集合转数组时请使用toArray(new T[0])或toArray(T[]::new)"}, {"rule": "squid:custom:MethodOverrideAnnotationChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/IssueSummaryBaseServiceImpl.java", "line": 140, "textRange": {"startLine": 139, "endLine": 140, "startOffset": 4, "endOffset": 100}, "message": "覆盖方法后必须添加@Override注解"}, {"rule": "squid:custom:MethodOverrideAnnotationChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/IssueSummaryBaseServiceImpl.java", "line": 361, "textRange": {"startLine": 361, "endLine": 361, "startOffset": 4, "endOffset": 101}, "message": "覆盖方法后必须添加@Override注解"}, {"rule": "squid:custom:MethodOverrideAnnotationChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/platform/scan/scanners/NewSonarScanner.java", "line": 121, "textRange": {"startLine": 121, "endLine": 121, "startOffset": 4, "endOffset": 66}, "message": "覆盖方法后必须添加@Override注解"}, {"rule": "squid:custom:MethodOverrideAnnotationChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/mr/rule/impl/MrLogbackPackagingDataRule.java", "line": 54, "textRange": {"startLine": 54, "endLine": 54, "startOffset": 8, "endOffset": 57}, "message": "覆盖方法后必须添加@Override注解"}, {"rule": "squid:custom:MethodOverrideAnnotationChecker", "location": "ks-serveree-themis-runner/src/main/java/com/kuaishou/serveree/themis/runner/task/KspProductAddLabelTask.java", "line": 62, "textRange": {"startLine": 62, "endLine": 62, "startOffset": 4, "endOffset": 55}, "message": "覆盖方法后必须添加@Override注解"}, {"rule": "squid:custom:MethodOverrideAnnotationChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/datasource/ThemisShardingSphereAutoConfiguration.java", "line": 91, "textRange": {"startLine": 91, "endLine": 91, "startOffset": 4, "endOffset": 63}, "message": "覆盖方法后必须添加@Override注解"}, {"rule": "squid:custom:MethodOverrideAnnotationChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/utils/HiveJdbcUtil.java", "line": 72, "textRange": {"startLine": 72, "endLine": 72, "startOffset": 20, "endOffset": 68}, "message": "覆盖方法后必须添加@Override注解"}, {"rule": "squid:custom:MethodOverrideAnnotationChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/IssueChangesServiceImpl.java", "line": 135, "textRange": {"startLine": 135, "endLine": 135, "startOffset": 4, "endOffset": 72}, "message": "覆盖方法后必须添加@Override注解"}, {"rule": "squid:custom:MethodOverrideAnnotationChecker", "location": "ks-serveree-themis-runner/src/main/java/com/kuaishou/serveree/themis/runner/task/QualityTaskKspSyncTask.java", "line": 54, "textRange": {"startLine": 54, "endLine": 54, "startOffset": 4, "endOffset": 55}, "message": "覆盖方法后必须添加@Override注解"}, {"rule": "squid:custom:LogParameterUseStringConcatChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/platform/notice/impl/CheckRepoNoticeActionServiceImpl.java", "line": 235, "textRange": {"startLine": 235, "endLine": 235, "startOffset": 16, "endOffset": 62}, "message": "禁止使用字符串拼接构造日志内容"}, {"rule": "squid:custom:LogParameterUseStringConcatChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/proxy/strategy/ParamsHandleService.java", "line": 52, "textRange": {"startLine": 52, "endLine": 52, "startOffset": 24, "endOffset": 84}, "message": "禁止使用字符串拼接构造日志内容"}, {"rule": "squid:custom:LogParameterUseStringConcatChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/utils/KconfUtil.java", "line": 46, "textRange": {"startLine": 46, "endLine": 46, "startOffset": 12, "endOffset": 68}, "message": "禁止使用字符串拼接构造日志内容"}, {"rule": "squid:custom:LogParameterUseStringConcatChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/check/QualityTaskKspSyncService.java", "line": 120, "textRange": {"startLine": 120, "endLine": 120, "startOffset": 12, "endOffset": 81}, "message": "禁止使用字符串拼接构造日志内容"}, {"rule": "squid:custom:LogParameterUseStringConcatChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/client/kdev/KdevApi.java", "line": 312, "textRange": {"startLine": 312, "endLine": 312, "startOffset": 16, "endOffset": 68}, "message": "禁止使用字符串拼接构造日志内容"}, {"rule": "findbugs:RCN_REDUNDANT_NULLCHECK_WOULD_HAVE_BEEN_A_NPE", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/client/compile/CompileApi.java", "line": 70, "textRange": {"startLine": 70, "endLine": 70, "startOffset": 0, "endOffset": 35}, "message": "Nullcheck of response at line 70 of value previously dereferenced in com.kuaishou.serveree.themis.component.client.compile.CompileApi.checkReleaseRootPomVersion(Integer, String, String, String)"}, {"rule": "squid:custom:LogParameterUseStringConcatChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/plugin/impl/SonarPluginServiceImpl.java", "line": 96, "textRange": {"startLine": 96, "endLine": 96, "startOffset": 12, "endOffset": 70}, "message": "禁止使用字符串拼接构造日志内容"}, {"rule": "squid:custom:LogParameterUseStringConcatChecker", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/kdev/impl/MrCheckpointCodeScanService.java", "line": 169, "textRange": {"startLine": 169, "endLine": 169, "startOffset": 12, "endOffset": 73}, "message": "禁止使用字符串拼接构造日志内容"}, {"rule": "findbugs:RCN_REDUNDANT_NULLCHECK_WOULD_HAVE_BEEN_A_NPE", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/client/git/SelfGitApi.java", "line": 283, "textRange": {"startLine": 283, "endLine": 283, "startOffset": 0, "endOffset": 35}, "message": "Nullcheck of response at line 283 of value previously dereferenced in com.kuaishou.serveree.themis.component.client.git.SelfGitApi.getProjectFilePathList(Integer, String)"}, {"rule": "squid:custom:LogParameterUseStringConcatChecker", "location": "ks-serveree-themis-runner/src/main/java/com/kuaishou/serveree/themis/runner/task/SonarBranchCleanTask.java", "line": 71, "textRange": {"startLine": 71, "endLine": 71, "startOffset": 8, "endOffset": 53}, "message": "禁止使用字符串拼接构造日志内容"}, {"rule": "squid:custom:LogParameterUseStringConcatChecker", "location": "ks-serveree-themis-runner/src/main/java/com/kuaishou/serveree/themis/runner/task/SonarBranchCleanTask.java", "line": 77, "textRange": {"startLine": 77, "endLine": 77, "startOffset": 8, "endOffset": 59}, "message": "禁止使用字符串拼接构造日志内容"}, {"rule": "squid:custom:LogParameterUseStringConcatChecker", "location": "ks-serveree-themis-runner/src/main/java/com/kuaishou/serveree/themis/runner/task/SonarBranchCleanTask.java", "line": 84, "textRange": {"startLine": 84, "endLine": 84, "startOffset": 8, "endOffset": 72}, "message": "禁止使用字符串拼接构造日志内容"}, {"rule": "findbugs:RCN_REDUNDANT_NULLCHECK_WOULD_HAVE_BEEN_A_NPE", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/utils/HiveJdbcUtil.java", "line": 46, "textRange": {"startLine": 46, "endLine": 46, "startOffset": 0, "endOffset": 62}, "message": "Nullcheck of statement at line 46 of value previously dereferenced in com.kuaishou.serveree.themis.component.utils.HiveJdbcUtil.executeQuery(String, long, int)"}, {"rule": "squid:__2__Shardingsphere__", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/datasource/ThemisDataSourceMapSetter.java", "line": 103, "textRange": {"startLine": 103, "endLine": 103, "startOffset": 16, "endOffset": 111}, "message": "你说啥呢"}, {"rule": "squid:__2__Shardingsphere__", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/datasource/ThemisDataSourceMapSetter.java", "line": 11, "textRange": {"startLine": 11, "endLine": 11, "startOffset": 0, "endOffset": 73}, "message": "你说啥呢"}, {"rule": "squid:__2__Shardingsphere__", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/config/datasource/ThemisDataSourceMapSetter.java", "line": 17, "textRange": {"startLine": 17, "endLine": 17, "startOffset": 0, "endOffset": 56}, "message": "你说啥呢"}, {"rule": "squid:custom:AdPostMethodChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/controller/SonarController.java", "line": 87, "textRange": {"startLine": 82, "endLine": 87, "startOffset": 4, "endOffset": 5}, "message": "禁止同时使用 @RequestParam 和 @PostMapping/@RequestMapping(method = RequestMethod.POST) 注解"}, {"rule": "squid:custom:AdPostMethodChecker", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/controller/SonarController.java", "line": 93, "textRange": {"startLine": 89, "endLine": 93, "startOffset": 4, "endOffset": 5}, "message": "禁止同时使用 @RequestParam 和 @PostMapping/@RequestMapping(method = RequestMethod.POST) 注解"}, {"rule": "findbugs:EQ_OVERRIDING_EQUALS_NOT_SYMMETRIC", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/SubClassTest.java", "line": 28, "textRange": {"startLine": 28, "endLine": 28, "startOffset": 0, "endOffset": 5}, "message": "com.kuaishou.serveree.themis.api.ww.SubClassTest overrides equals in Department and may not be symmetric"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/LocalServiceImpl.java", "line": 330, "textRange": {"startLine": 330, "endLine": 330, "startOffset": 25, "endOffset": 85}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/PlatformUserRoleRelationServiceImpl.java", "line": 74, "textRange": {"startLine": 73, "endLine": 74, "startOffset": 25, "endOffset": 123}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/TaskServiceImpl.java", "line": 115, "textRange": {"startLine": 115, "endLine": 115, "startOffset": 26, "endOffset": 86}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/CheckFileIssueServiceImpl.java", "line": 81, "textRange": {"startLine": 81, "endLine": 81, "startOffset": 25, "endOffset": 99}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/CheckFileIssueServiceImpl.java", "line": 92, "textRange": {"startLine": 92, "endLine": 92, "startOffset": 25, "endOffset": 100}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/IssueSummaryBaseServiceImpl.java", "line": 174, "textRange": {"startLine": 174, "endLine": 174, "startOffset": 25, "endOffset": 96}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/platform/impl/PlatformFileMeasureServiceImpl.java", "line": 197, "textRange": {"startLine": 197, "endLine": 197, "startOffset": 33, "endOffset": 89}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/process/impl/ProcessCheckServiceImpl.java", "line": 291, "textRange": {"startLine": 291, "endLine": 291, "startOffset": 59, "endOffset": 109}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/process/impl/ProcessCheckServiceImpl.java", "line": 292, "textRange": {"startLine": 292, "endLine": 292, "startOffset": 71, "endOffset": 118}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/QualityTaskKspSyncServiceImpl.java", "line": 58, "textRange": {"startLine": 58, "endLine": 58, "startOffset": 38, "endOffset": 98}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/QualityTaskKspSyncServiceImpl.java", "line": 66, "textRange": {"startLine": 66, "endLine": 66, "startOffset": 29, "endOffset": 79}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-runner/src/main/java/com/kuaishou/serveree/themis/runner/kafka/PlatformScanResultSaveFileDataConsumer.java", "line": 278, "textRange": {"startLine": 276, "endLine": 278, "startOffset": 25, "endOffset": 118}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-runner/src/main/java/com/kuaishou/serveree/themis/runner/kafka/PlatformScanResultSaveFileDataConsumer.java", "line": 360, "textRange": {"startLine": 360, "endLine": 360, "startOffset": 25, "endOffset": 89}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/check/QualityTaskKspSyncService.java", "line": 130, "textRange": {"startLine": 130, "endLine": 130, "startOffset": 66, "endOffset": 116}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/check/QualityTaskKspSyncService.java", "line": 132, "textRange": {"startLine": 132, "endLine": 132, "startOffset": 16, "endOffset": 76}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/platform/impl/PlatformSonarInteractiveServiceImpl.java", "line": 99, "textRange": {"startLine": 98, "endLine": 99, "startOffset": 20, "endOffset": 74}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/ProjectServiceImpl.java", "line": 45, "textRange": {"startLine": 45, "endLine": 45, "startOffset": 49, "endOffset": 118}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/ProjectServiceImpl.java", "line": 49, "textRange": {"startLine": 49, "endLine": 49, "startOffset": 46, "endOffset": 108}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/check/run/SonarRunCheckService.java", "line": 92, "textRange": {"startLine": 92, "endLine": 92, "startOffset": 42, "endOffset": 100}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-runner/src/main/java/com/kuaishou/serveree/themis/runner/task/CompareProfileRulesBetweenDbAndSonarTask.java", "line": 201, "textRange": {"startLine": 201, "endLine": 201, "startOffset": 38, "endOffset": 93}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/QualityCheckServiceImpl.java", "line": 1217, "textRange": {"startLine": 1216, "endLine": 1217, "startOffset": 25, "endOffset": 67}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/sonar/impl/SonarServiceImpl.java", "line": 1053, "textRange": {"startLine": 1053, "endLine": 1053, "startOffset": 42, "endOffset": 107}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/sonar/impl/SonarServiceImpl.java", "line": 1171, "textRange": {"startLine": 1171, "endLine": 1171, "startOffset": 25, "endOffset": 90}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/CheckFileServiceImpl.java", "line": 114, "textRange": {"startLine": 114, "endLine": 114, "startOffset": 43, "endOffset": 101}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/CheckFileServiceImpl.java", "line": 98, "textRange": {"startLine": 98, "endLine": 98, "startOffset": 56, "endOffset": 114}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/IssueSummaryServiceImpl.java", "line": 267, "textRange": {"startLine": 267, "endLine": 267, "startOffset": 25, "endOffset": 82}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/platform/impl/PlatformRuleServiceImpl.java", "line": 1140, "textRange": {"startLine": 1140, "endLine": 1140, "startOffset": 44, "endOffset": 104}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/CheckProfileRuleRelationServiceImpl.java", "line": 172, "textRange": {"startLine": 171, "endLine": 172, "startOffset": 16, "endOffset": 96}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/sonar/JavaMavenSonarReportService.java", "line": 267, "textRange": {"startLine": 267, "endLine": 267, "startOffset": 46, "endOffset": 108}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/sonar/JavaMavenSonarReportService.java", "line": 269, "textRange": {"startLine": 269, "endLine": 269, "startOffset": 25, "endOffset": 94}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/platform/impl/PlatformCheckActionServiceImpl.java", "line": 651, "textRange": {"startLine": 651, "endLine": 651, "startOffset": 34, "endOffset": 90}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/ThemisRuleServiceImpl.java", "line": 73, "textRange": {"startLine": 73, "endLine": 73, "startOffset": 25, "endOffset": 85}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/client/sonar/operations/SonarOperations.java", "line": 440, "textRange": {"startLine": 440, "endLine": 440, "startOffset": 52, "endOffset": 114}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "squid:S20002", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/check/report/JavaStaticCheckReportService.java", "line": 97, "textRange": {"startLine": 97, "endLine": 97, "startOffset": 62, "endOffset": 112}, "message": "可能发生Key冲突的toMap方法"}, {"rule": "findbugs:URF_UNREAD_FIELD", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/vo/response/ProfileUpdateRuleResponse.java", "line": 10, "textRange": {"startLine": 10, "endLine": 10, "startOffset": 0, "endOffset": 8}, "message": "Unread field: com.kuaishou.serveree.themis.component.vo.response.ProfileUpdateRuleResponse.updateResult"}, {"rule": "findbugs:NP_ALWAYS_NULL", "location": "ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/RefreshableLocalCache.java", "line": 101, "textRange": {"startLine": 101, "endLine": 101, "startOffset": 0, "endOffset": 44}, "message": "Null pointer dereference of name in com.kuaishou.serveree.themis.api.ww.RefreshableLocalCache.main(String[])"}, {"rule": "findbugs:EQ_OVERRIDING_EQUALS_NOT_SYMMETRIC", "location": "ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/common/entity/IssueGroupPanel.java", "line": 18, "textRange": {"startLine": 18, "endLine": 18, "startOffset": 0, "endOffset": 37}, "message": "com.kuaishou.serveree.themis.component.common.entity.IssueGroupPanel overrides equals in IssueSummary and may not be symmetric"}, {"rule": "findbugs:NP_NONNULL_RETURN_VIOLATION", "location": "ks-serveree-themis-runner/src/main/java/com/kuaishou/serveree/themis/runner/task/GenerateCommonUniqIdTask.java", "line": 28, "textRange": {"startLine": 28, "endLine": 28, "startOffset": 0, "endOffset": 20}, "message": "com.kuaishou.serveree.themis.runner.task.GenerateCommonUniqIdTask.name() may return null, but is declared @Nonnull"}, {"rule": "findbugs:NP_NONNULL_RETURN_VIOLATION", "location": "ks-serveree-themis-runner/src/main/java/com/kuaishou/serveree/themis/runner/task/GenerateCommonUniqIdTask.java", "line": 39, "textRange": {"startLine": 39, "endLine": 39, "startOffset": 0, "endOffset": 20}, "message": "com.kuaishou.serveree.themis.runner.task.GenerateCommonUniqIdTask.bizDef() may return null, but is declared @Nonnull"}]