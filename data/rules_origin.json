{"squid:S2111": {"title": "不应该使用BigDecimal(double)", "htmlDesc": "<p>由于浮点不精确，您不太可能从 BigDecimal(double) 构造函数中获得预期的值。</p>\n<p>在<a href=\"http://docs.oracle.com/javase/7/docs/api/java/math/BigDecimal.html#BigDecimal(double)\">the JavaDocs中</a>:</p>\n<blockquote>\n这个构造函数的结果可能有些不可预测。有人可能会认为在Java中编写new BigDecimal(0.1)会创建一个精确等于0.1的BigDecimal对象（一个无标度值为1，标度为1的BigDecimal），但实际上它等于0.1000000000000000055511151231257827021181583404541015625。这是因为0.1不能在double类型中精确表示（事实上，也不能在任何有限长度的二进制小数中精确表示）。因此，传递给构造函数的值与0.1并不完全相等，尽管外观上如此。\n</blockquote>\n<p>相反，您应该使用 BigDecimal.valueOf，它在幕后使用字符串来消除浮点舍入错误，或者使用采用 String 参数的构造函数。</p>\n<h2>不合规的代码示例</h2>\n<pre>\ndouble d = 1.1;\n\nBigDecimal bd1 = new BigDecimal(d); // 错误使用\nBigDecimal bd2 = new BigDecimal(1.1); // 错误使用\n</pre>\n<h2>合规的代码示例</h2>\n<p></p>\n<pre>\ndouble d = 1.1;\n\nBigDecimal bd1 = BigDecimal.valueOf(d);\nBigDecimal bd2 = new BigDecimal(\"1.1\"); // 使用字符串构造函数\n</pre>\n<h2>查看</h2>\n<ul>\n  <li> <a href=\"https://www.securecoding.cert.org/confluence/x/NQAVAg\">CERT, NUM10-J.</a> - 请不要用浮点型数据构造BigDecimal对象。</li>\n</ul>\n"}, "squid:custom:MethodOverrideAnnotationChecker": {"title": "规范——覆盖方法后禁止移除Override注解", "htmlDesc": "<p>【强制】所有覆写的方法，必须加@Override注解。</p>\n<h2>不合规的代码示例</h2>\n<pre>\nstatic abstract class AClass {\n  abstract void foo();\n}\n\nstatic class AImpl extends AClass {\n  // 禁止移除 @Override\n  void foo() {\n\n  }\n}\n</pre>\n<h2>合规的代码示例</h2>\n<pre>\nstatic abstract class AClass {\n  abstract void foo();\n}\n\nstatic class AImpl extends AClass {\n\n  @Override\n  void foo() {\n\n  }\n}\n</pre>"}, "squid:custom:SelectSqlNoLimitChecker": {"title": "Select语句必须加上limit字段", "htmlDesc": "<p>除使用queryForObject进行查询的场景外，Select语句必须加上limit字段</p>\n<h2>不合规的代码示例</h2>\n<pre>\n    public int queryStatisticByDt(String dt) {\n        String sql = \"select id, time,create_time, merchant_id, account_group_key, balance_type, statistic_type,\"// 不合规\n                + \"amount, count, fen  from ks_pay.kspay_statement_statistic_sf2021 where dt=:dt\";\n        MapSqlParameterSource paramSource = new MapSqlParameterSource()\n                .addValue(\"dt\", dt);\n        return getHiveTemplate(CHINA).query(sql, paramSource, statementStatisticRowMapper);\n    }\n</pre>\n<h2>合规的代码示例</h2>\n<pre>\n    public int queryStatisticByDt1(String dt) {\n        String sql = \"select id, time,create_time, merchant_id, account_group_key, balance_type, statistic_type,\"//合规\n                + \"amount, count, fen  from ks_pay.kspay_statement_statistic_sf2021 where id <= :id and account_group_key = :accountGroupKey order by id desc limit :limit\";\n        MapSqlParameterSource paramSource = new MapSqlParameterSource()\n                .addValue(\"dt\", dt);\n        return getHiveTemplate(CHINA).query(sql, paramSource, statementStatisticRowMapper);\n    }\n\n    public Object getByAccountDepositNoInTrx(@Nonnull NamedParameterJdbcTemplate tpl,\n            @Nonnull String merchantId, @Nonnull String accountId,\n            @Nonnull String accountDepositNo) {\n        String sql =\n                String.format(\"select * from %s where account_deposit_no=:accountDepositNo and account_id=:accountId\",\n                        tableName(merchantId, accountId));\n        MapSqlParameterSource params = new MapSqlParameterSource(\"accountDepositNo\", accountDepositNo)\n                .addValue(\"accountId\", accountId);\n        try {\n            return tpl.queryForObject(sql, params, rowMapper); //合规\n        } catch (EmptyResultDataAccessException e) {\n            return null;\n        }\n    }\n</pre>"}, "squid:custom:AdFixedDomainChecker": {"title": "商业化——安全生产域名逃生容灾要求代码中不能硬编码CDN域名", "htmlDesc": "<h2>安全生产域名逃生容灾要求代码中不能硬编码CDN域名</h2>\n<p>安全生产域名逃生容灾要求代码中不能出现属于「非规范二级域名」但不在「CDN规范三级域名」写死域名的场景</p>\n<h2>不合规的代码示例</h2>\n<pre>\n    private static final String PROTOCOL_URL = \"https://tx-ad.a.yximgs.com/kos/nlav11628/agreements/lingluproto.pdf\";\n</pre>\n<h2>非规范二级域名列表</h2>\n<pre>\n    \"yximgs.com\",\n    \"kwai.net\",\n    \"streamlake.cn\",\n    \"streamlake.com\",\n    \"kwimgs.com\",\n    \"ap4r.com\",\n    \"snackvideo.in\",\n    \"kwaicdn.com\",\n    \"cdn.com\",\n    \"gskwai.com\",\n    \"adbkwai.com\",\n    \"adukwai.com\",\n    \"etoote.com\",\n    \"akamaized.net\",\n    \"adkwai.com\",\n    \"itotio.com\",\n    \"live-voip.com\",\n    \"ssrcdn.com\",\n    \"kwai.com\",\n    \"video-voip.com\",\n    \"myqcloud.com\",\n    \"kskwai.com\",\n    \"kspkg.com\",\n    \"shukwai.com\",\n    \"shbkwai.com\",\n    \"ecukwai.com\",\n    \"becukwai.com\",\n    \"aisee.tv\",\n    \"inkwai.com\",\n    \"eckwai.com\",\n    \"ssscdn.com\",\n    \"upcdn.net\",\n    \"meituan.net\",\n    \"oskwai.com\",\n    \"acfun.cn\",\n    \"cloudfront.net\",\n    \"zynn.net\",\n    \"inkuai.com\",\n    \"joyo.sg\",\n    \"beckwai.com\",\n    \"paykwai.com\",\n    \"hapame.com\",\n    \"ks-cdn.com\",\n    \"ndcyx.com\",\n    \"shwkwai.com\",\n    \"dreamriverclub.com\",\n    \"kwaionline.com\",\n    \"lontv.cn\",\n    \"dewu.com\",\n    \"safetykwai.com\",\n    \"fdlbeckwai.com\",\n    \"safetystatic.com\",\n    \"fdleckwai.com\",\n    \"safetyimg.com\",\n    \"bpaykwai.com\",\n    \"safetyvod.com\",\n    \"dnsv1.com\",\n    \"guangdongyunchen.com\",\n    \"playcasualgame.cn\",\n    \"aixifan.com\",\n    \"syncdn.cn\",\n    \"kwaicdnx.com\",\n    \"pq8.club\",\n    \"qingque.cn\",\n    \"ndcimgs.com\",\n    \"golden49.net\",\n    \"amazonaws.com\",\n    \"snackvideo.com\",\n    \"ks-la.com\",\n    \"uvideo.org\",\n    \"uvideo.info\",\n    \"fortyniners49.net\",\n    \"a49m.com\",\n    \"inscoming.com\",\n    \"dropzzz.net\",\n    \"zikzak.tv\"\n</pre>\n<h2>各部门规范三级域名</h2>\n<pre>\n以下*仅包含1，2，3，4，5\n商业化CDN规范域名：\n    p*-ad.adkwai.com\n    v*-ad.adkwai.com\n    p*-ad.adkwai.com\n    p*-ad.adukwai.com\n    v*-ad.adukwai.com\n    p*-ad-fdl.adukwai.com\n    v*-ad-fdl.adukwai.com\n    p*-ad.adbkwai.com\n    v*-ad.adbkwai.com\n    p*-ad.adbkwai.com\n    p*-ad.adbkwai.com\n    v*-ad.adbkwai.com\n    p*-ad-fdl.adbkwai.com\n    v*-ad-fdl.adbkwai.com\n电商CDN规范域名：\n    p[1~5]-ec.eckwai.com\n    d[1~5]-ec.eckwai.com\n    p[1~5]-ec.ecukwai.com\n    v[1~5]-ec.ecukwai.com\n    d[1~5]-ec.ecukwai.com\n    p[1~5]-ec-fdl.ecukwai.com\n    v[1~5]-ec-fdl.ecukwai.com\n    d[1~5]-ec-fdl.ecukwai.com\n    p[1~5]-ec.beckwai.com\n    d[1~5]-ec.beckwai.com\n    p[1~5]-ec.becukwai.com\n    v[1~5]-ec.becukwai.com\n    d[1~5]-ec.becukwai.com\n    p[1~5]-ec-fdl.becukwai.com\n    p[1~5]-ec-fdl.becukwai.com\n    d[1~5]-ec-fdl.becukwai.com\n本地CDN规范域名：\n    p[1-5]-sh.shwkwai.com\n    p[1-5]-sh.shukwai.com\n    p[1-5]-sh.shbkwai.com\n    d[1-5]-sh.shukwai.com\n    d[1-5]-sh.shbkwai.com\n    v[1-5]-sh.shukwai.com\n    v[1-5]-sh.shbkwai.com\n    p[1-5]-sh-fdl.shukwai.com\n    p[1-5]-sh-fdl.shbkwai.com\n    d[1-5]-sh-fdl.shukwai.com\n    d[1-5]-sh-fdl.shbkwai.com\n    v[1-5]-sh-fdl.shukwai.com\n    v[1-5]-sh-fdl.shbkwai.com\n支付CDN规范域名：\n    w1.paykwai.com\n    w2.paykwai.com\n    w1.bpaykwai.com\n    w2.bpaykwai.com\n</pre>"}, "squid:S2068": {"title": "凭证不应该被硬编码", "htmlDesc": "<p>由于从已编译的应用程序中提取字符串很容易，因此凭据不应该硬编码。这样做几乎可以保证它们最终会落入攻击者手中，尤其是对于分发的应用程序而言。</p>\n<p>凭据应该存储在代码之外，以强加密的配置文件或数据库中，确保其安全保护。</p>\n<p>建议使用其他凭据词语（例如\"oauthToken\"、\"secret\"等）自定义此规则的配置。</p>\n<h2>不合规的代码示例</h2>\n<pre>\nConnection conn = null;\ntry {\n  String uname = \"steve\";\n  String password = \"blue\";\n  conn = DriverManager.getConnection(\"********************************=\" + uname + \"&password=\" + password); // 不合规\n}\n</pre>\n<h2>合规的代码示例</h2>\n<pre>\nConnection conn = null;\ntry {\n  String uname = getEncryptedUser();\n  String password = getEncryptedPass();\n  conn = DriverManager.getConnection(\"********************************=\" + uname + \"&password=\" + password); // 合规\n}\n</pre>\n<h2>查看更多</h2>\n<ul>\n  <li> <a href=\"https://www.owasp.org/index.php/Top_10-2017_A2-Broken_Authentication\">OWASP Top 10 2017 Category A2</a> - 身份认证被破坏 </li>\n  <li> <a href=\"http://cwe.mitre.org/data/definitions/798\">MITRE, CWE-798</a> - 使用硬编码凭证 </li>\n  <li> <a href=\"http://cwe.mitre.org/data/definitions/259\">MITRE, CWE-259</a> - 使用硬编码密码 </li>\n  <li> <a href=\"https://www.securecoding.cert.org/confluence/x/qQCHAQ\">CERT, MSC03-J.</a> - 永远不要硬编码敏感信息 </li>\n  <li> <a href=\"https://www.sans.org/top25-software-errors/#cat3\">SANS Top 25</a> - 渗透性防御 </li>\n  <li> 源自 FindSecBugs 规则 <a href=\"http://h3xstream.github.io/find-sec-bugs/bugs.htm#HARD_CODE_PASSWORD\">硬编码密码</a> </li>\n</ul>\n"}, "squid:custom:ManualCreateThreadChecker": {"title": "规范——禁止在应用中显式创建线程", "htmlDesc": "<p>【强制】线程资源必须通过线程池提供，不允许在应用中自行显式创建线程。</p>\n<h2>不合规的代码示例</h2>\n<pre>\npublic void foo() {\n  // 禁止使用 new Thread()\n  new Thread(() -> doSomething()).start();\n}\n</pre>\n<h2>合规的代码示例</h2>\n<pre>\n@Autowired\nprivate ThreadPoolExecutor executor;\n\npublic void foo() {\n  executor.submit(() -> doSomething());\n}\n</pre>"}, "squid:custom:ThreadPoolCreateMethodChecker": {"title": "规范——创建线程池必须使用公司组件ExecutorEx和DynamicExecutor", "htmlDesc": "<p>【强制】使用基础架构封装的ExecutorsEx和DynamicThreadExecutor工厂方法创建线程池，支持上下文传递、监控利用率、拦截异常日志、动态调整线程数等功能。</p>\n<h2>不合规的代码示例</h2>\n<pre>\nclass GH1 {\n    private ThreadFactory threadFactory = new CustomizableThreadFactory(\"poolPrefix-\");\n    ThreadPoolExecutor taskCheckExecutor3() {\n        return new ThreadPoolExecutor( // Noncompliant\n            6,\n            6,\n            10,\n            TimeUnit.MINUTES,\n            new PriorityBlockingQueue<>(),\n            threadFactory\n        );\n    }\n    ThreadPoolExecutor taskCheckExecutor5() {\n        Executors.newCachedThreadPool(); // Noncompliant\n        Executors.newCachedThreadPool(threadFactory); // Noncompliant\n        Executors.newFixedThreadPool(1); // Noncompliant\n        Executors.newFixedThreadPool(1, threadFactory); // Noncompliant\n        Executors.newSingleThreadExecutor(); // Noncompliant\n        Executors.newSingleThreadExecutor(threadFactory); // Noncompliant\n        Executors.newWorkStealingPool(); // Noncompliant\n        Executors.newWorkStealingPool(1); // Noncompliant\n        return null;\n    }\n}\n</pre>\n<h2>合规的代码示例</h2>\n<pre>\n@Bean\npublic ThreadPoolExecutor threadPoolExecutor() {\n  return ExecutorsEx.newBlockingThreadPool(1, \"taskPool-%d\");\n}\n\n@Bean\npublic ExecutorService threadPoolExecutor() {\n  return DynamicThreadExecutor.dynamic(() -> KCONF_DYNAMIC_THREAD_COUNT_MAP.get().get(\"asyncPool\"), \"ssss\");\n}\n</pre>"}, "squid:custom:LongVariableUseLowerCaseIChecker": {"title": "规范——禁止使用小写l为长整形变量赋值", "htmlDesc": "<p>【强制】在long或者Long赋值时，数值后使用大写字母L，不能是小写字母l，小写容易跟数字混淆，造成误解。</p>\n<h2>不合规的代码示例</h2>\n<pre>\npublic static void main(String[] args) {\n  long a = 5l; // 禁止使用小写l\n}\n</pre>\n<h2>合规的代码示例</h2>\n<pre>\npublic static void main(String[] args) {\n  long a = 5L;\n}\n</pre>"}, "squid:custom:StaticThreadLocalAndScopeChecker": {"title": "规范——ThreadLocal和ScopeKey类型变量必须添加static", "htmlDesc": "<p>【强制】ThreadLocal、Scope等变量必须被static修饰。</p>\n<h2>不合规的代码示例</h2>\n<pre>\nclass StaticVariable {\n    private final ThreadLocal&lt;Object&gt; test1 = new ThreadLocal<>(); // Noncompliant\n    private final ScopeKey&lt;String&gt; scopeKey1 = ScopeKey.allocate(); // Noncompliant\n}\n</pre>\n<h2>合规的代码示例</h2>\n<pre>\nclass StaticVariable {\n    private static final ThreadLocal&lt;Object&gt; test2 = new ThreadLocal<>(); // Compliant\n    private static final ScopeKey&lt;String&gt; scopeKey2 = ScopeKey.allocate(); // Compliant\n}\n</pre>"}, "findbugs:DMI_BIGDECIMAL_CONSTRUCTED_FROM_DOUBLE": {"title": "正确性 - BigDecimal 由未精确表示的 double 构造", "htmlDesc": "<p>代码使用无法很好地转换为十进制数的双精度值创建 BigDecimal。 例如，人们可能会假设在 Java 中编写 new BigDecimal(0.1) 会创建一个恰好等于 0.1（未缩放的值 1，小数位数为 1）的 BigDecimal，但它实际上等于 0.1000000000000000055511151231257827021181583404541015625。 您可能想要使用 BigDecimal.valueOf(double d) 方法，该方法使用 double 的字符串表示形式来创建 BigDecimal（例如，BigDecimal.valueOf(0.1) 给出 0.1）。</p>\n<h2>不合规的代码示例</h2>\n<pre>\nBigDecimal decimal = new BigDecimal(0.1); // 使用double类型参数的构造函数，由于精度损失，实际与预期不符\n</pre>\n<h2>合规的代码示例</h2>\n<pre>\nBigDecimal decimal = BigDecimal.valueOf(0.1); // 使用静态方法构造\ndecimal = new BigDecimal(\"0.1\"); // 或使用字符串类型参数的构造方法\n</pre>\n"}, "findbugs:RV_ABSOLUTE_VALUE_OF_HASHCODE": {"title": "正确性 - 计算有符号 32 位哈希码绝对值的错误尝试", "htmlDesc": "<p>此代码生成一个哈希码，然后计算该哈希码的绝对值。 如果哈希码是 Integer.MIN_VALUE，则结果也将为负数（因为 Math.abs(Integer.MIN_VALUE) == Integer.MIN_VALUE）。\n2^32 个字符串中有一个的 hashCode 为 Integer.MIN_VALUE，包括\"polygenelubricants\"、\"GydZG_\"和\"DESIGNING WORKHOUSES\"。</p>\n<h2>代码示例</h2>\n<pre>\nint hash = \"polygenelubricants\".hashCode(); // \"polygenelubricants\" 的 hashCode()为Integer.MIN_VALUE\nSystem.out.println(Math.abs(hash) > 0); // false，Math.abs(Integer.MIN_VALUE) == Integer.MIN_VALUE < 0\nSystem.out.println(Math.abs(hash) == Integer.MIN_VALUE); // true\n</pre>\n"}, "squid:__2__Shardingsphere__": {"title": "测试2——Shardingsphere检测", "htmlDesc": "&#20320;&#35828;&#21861;&#21602;"}, "findbugs:SIC_INNER_SHOULD_BE_STATIC": {"title": "性能 - 应该是静态内部类", "htmlDesc": "<p>这个类是一个内部类，但它没有使用到外部类 OuterClass 的引用。然而，由于它是一个非静态内部类，每个 InnerClass 的实例都会隐式地持有对外部类 OuterClass 对象的引用，这会增加实例的大小并且可能会使得对 OuterClass 对象的引用保持时间比必要的更长。</p>\n<h2>不合规的代码示例</h2>\n<pre>\npublic class OuterClass {\n    private int value;\n\n    public void doSomething() {\n        InnerClass inner = new InnerClass();\n    }\n\n    private class InnerClass {                  // 非静态内部类，不合规\n        public InnerClass() {\n            System.out.println(\"InnerClass\");\n        }\n    }\n}\n</pre>\n<h2>合规的代码示例</h2>\n<p>修复方法是将内部类声明为静态类，以消除对外部类对象的引用。</p>\n<pre>\npublic class OuterClass {\n    private int value;\n\n    public void doSomething() {\n        InnerClass inner = new InnerClass();\n    }\n\n    private class InnerClass {                  // 静态内部类，合规\n        public InnerClass() {\n            System.out.println(\"InnerClass\");\n        }\n    }\n}\n</pre>\n"}, "squid:custom:ThreadPoolThreadNameParameterChecker": {"title": "规范——创建线程池时必须提供有意义的线程命名参数", "htmlDesc": "<p>【强制】线程池必须指定有意义的名称，便于监控和 jstack 区分。</p>\n<h2>不合规的代码示例</h2>\n<pre>\nclass GH {\n    ThreadPoolExecutor taskCheckExecutor1() {\n        return new ThreadPoolExecutor( // Noncompliant\n                6,\n                6,\n                10,\n                TimeUnit.MINUTES,\n                new PriorityBlockingQueue<>()\n        );\n    }\n    ThreadPoolExecutor taskCheckExecutor2() {\n        return new ThreadPoolExecutor( // Noncompliant\n                6,\n                6,\n                10,\n                TimeUnit.MINUTES,\n                new PriorityBlockingQueue<>(),\n                new AbortPolicy()\n        );\n    }\n    ThreadPoolExecutor taskCheckExecutor3() {\n        return new ThreadPoolExecutor( // Noncompliant\n                6,\n                6,\n                10,\n                TimeUnit.MINUTES,\n                new PriorityBlockingQueue<>(),\n                Executors.defaultThreadFactory()\n        );\n    }\n    ThreadPoolExecutor taskCheckExecutor4() {\n        return new ThreadPoolExecutor( // Noncompliant\n                6,\n                6,\n                10,\n                TimeUnit.MINUTES,\n                new PriorityBlockingQueue<>(),\n                Executors.defaultThreadFactory(),\n                new AbortPolicy()\n        );\n    }\n    ThreadPoolExecutor taskCheckExecutor5() {\n        new ScheduledThreadPoolExecutor(1); // Noncompliant\n        new ScheduledThreadPoolExecutor(1, Executors.defaultThreadFactory()); // Noncompliant\n        new ScheduledThreadPoolExecutor(1, Executors.defaultThreadFactory(), new AbortPolicy()); // Noncompliant\n        Executors.newCachedThreadPool(); // Noncompliant\n        Executors.newCachedThreadPool(Executors.defaultThreadFactory()); // Noncompliant\n        Executors.newFixedThreadPool(1, Executors.defaultThreadFactory()); // Noncompliant\n        Executors.newFixedThreadPool(1); // Noncompliant\n        Executors.newSingleThreadExecutor(); // Noncompliant\n        Executors.newSingleThreadExecutor(Executors.defaultThreadFactory()); // Noncompliant\n        Executors.newScheduledThreadPool(1); // Noncompliant\n        Executors.newScheduledThreadPool(1, Executors.defaultThreadFactory()); // Noncompliant\n        Executors.newSingleThreadScheduledExecutor(); // Noncompliant\n        Executors.newSingleThreadScheduledExecutor(Executors.defaultThreadFactory()); // Noncompliant\n        Executors.newWorkStealingPool(); // Noncompliant\n        Executors.newWorkStealingPool(1); // Noncompliant\n        return null;\n    }\n}\n</pre>\n<h2>合规的代码示例</h2>\n<pre>\n@Bean\nThreadPoolExecutor taskCheckExecutor() {\n  return new ThreadPoolExecutor(\n    6,\n    6,\n    10,\n    TimeUnit.MINUTES,\n    new PriorityBlockingQueue<>(),\n    new CustomizableThreadFactory(\"taskCheck-\")\n  );\n}\n</pre>"}, "squid:custom:FloatingPointCalculationChecker": {"title": "浮点数运算可能会有精度丢失问题, 建议改造为BigDecimal", "htmlDesc": "<h2>浮点数运算可能会有精度丢失问题, 建议改造为BigDecimal</h2>"}, "squid:custom:BasicOrWrapperFloatCompareChecker": {"title": "规范——浮点数比较禁止使用==或equals", "htmlDesc": "<p>【强制】浮点数之间的等值判断，基本数据类型禁止用==比较，包装类型禁止用equals来判断。比较应当使用差值范围来计算，或者使用BigDecimal。</p>\n<h2>不合规的代码示例</h2>\n<pre>\nclass FloatDemo {\n    public static void main(String[] args) {\n        float f = 1.2f;\n        Float ff = 1.2f, fff = 1.2f;\n        double d = 1.2d;\n        Double dd = 1.2d, ddd = 1.3d;\n        if (f == d) { // Noncompliant\n\n        }\n        if (ff == fff) { // Noncompliant\n\n        }\n        if (f == ff) { // Noncompliant\n\n        }\n        if (d == dd.doubleValue()) { // Noncompliant\n\n        }\n        if (dd == ddd) { // Noncompliant\n\n        }\n        if (dd.equals(ddd)) { // Noncompliant\n\n        }\n        if (dd.equals(d)) { // Noncompliant\n\n        }\n        if (d == (1.0 + 2.5)) { // Noncompliant\n\n        }\n        if (dd == 1.0 * 2.3) { // Noncompliant\n\n        }\n    }\n}\n</pre>\n<h2>合规的代码示例</h2>\n<pre>\npublic boolean compare(double a, double b) {\n  return new BigDecimal(String.valueOf(a)).compareTo(new BigDecimal(String.valueOf(b))) == 0;\n}\n\npublic boolean compare(Double a, Double b) {\n  return new BigDecimal(String.valueOf(a)).compareTo(new BigDecimal(String.valueOf(b))) == 0;\n}\n</pre>"}, "squid:custom_KspayForbiddenBigDecimalEqualsMethodChecker": {"title": "支付——禁止使用BigDecimal.equals方法", "htmlDesc": "&#25903;&#20184;&mdash;&mdash;BigDecimal&#19981;&#20801;&#35768;&#20351;&#29992;equals&#26041;&#27861;&#27604;&#36739;&#65292;&#35201;&#29992;compare&#26041;&#27861;&#27604;&#36739;<br/><h2>&#19981;&#21512;&#35268;&#30340;&#20195;&#30721;&#31034;&#20363;\n</h2><pre><code>BigDecimal bigDecimal1 = new BigDecimal(&quot;0.1&quot;);\nBigDecimal bigDecimal2 = new BigDecimal(&quot;0.10&quot;);\nSystem.out.println(bigDecimal1.equals(bigDecimal2));// &#19981;&#21512;&#35268;</code></pre><br/><h2>&#21512;&#35268;&#30340;&#20195;&#30721;&#31034;&#20363;\n</h2><pre><code>BigDecimal bigDecimal1 = new BigDecimal(&quot;0.1&quot;);\nBigDecimal bigDecimal2 = new BigDecimal(&quot;0.10&quot;);\nSystem.out.println(bigDecimal1.compareTo(bigDecimal2));// &#21512;&#35268;</code></pre>"}, "findbugs:RCN_REDUNDANT_NULLCHECK_WOULD_HAVE_BEEN_A_NPE": {"title": "正确性 - 对已经解引用过的值进行 Nullcheck", "htmlDesc": "<p>代码检查一个值是否为 null，但是这个值之前已经被解引用过了，如果它为 null，那么在之前的解引用处就应该发生空指针异常。实际上，这段代码和之前的解引用对于这个值是否允许为 null 存在分歧。要么这个检查是多余的，要么之前的解引用是错误的。</p>\n<h2>不合规的代码示例</h2>\n<pre>\npublic class ExampleClass {\n    public void process(String str) {\n        System.out.println(str.hashCode());\n        if (str != null) {                    // 上一行代码已经对str进行了解引用。不合规\n            System.out.println(str.length()); \n        }\n    }\n\n    public static void main(String[] args) {\n        ExampleClass example = new ExampleClass();\n        example.process(null);\n    }\n}\n</pre>\n<h2>合规的代码示例</h2>\n<p>修复方法是要么移除后面的判空检查，要么修复之前的解引用错误。如果之前的解引用是错误的，需要确保在解引用之前进行 null 值的检查。</p>\n<pre>\npublic class ExampleClass {\n    public void process(String str) { \n        if (str != null) {                    // 先判空。合规\n            System.out.println(str.hashCode());\n            System.out.println(str.length()); \n        }\n    }\n\n    public static void main(String[] args) {\n        ExampleClass example = new ExampleClass();\n        example.process(null);\n    }\n}\n</pre>\n"}, "squid:custom:EnumGetNameChecker": {"title": "枚举类禁止定义名称为name的成员变量", "htmlDesc": "<h2>禁用Enum中的name变量命名避免产生歧义</h2>"}, "findbugs:EQ_OVERRIDING_EQUALS_NOT_SYMMETRIC": {"title": "正确性 - equals 方法覆盖超类中的 equals 并且可能不满足对称性", "htmlDesc": "<p>该类定义了一个覆盖超类的equals方法。两个equals方法都使用instanceof运算符来确定两个对象是否相等。然而，这种做法存在风险，因为equals方法必须满足对称性（换句话说，a.equals(b)与b.equals(a)结果一致）。如果B是A的子类，并且A的equals方法检查参数是否是A类的实例，B的equals方法则检查参数是否是B类的实例，那么这两个方法所定义的等价关系很可能不是对称的。</p>\n<h2>不合规的代码示例</h2>\n<pre>\nclass A {\n    // 字段、构造函数和其他方法\n\n    @Override\n    public boolean equals(Object obj) {\n        if (this == obj) {\n            return true;\n        }\n        if (obj instanceof A) { // 检查参数是否是A类的实例\n            // 当obj是A类的实例时才会返回true\n            return true;\n        }\n        return false;\n    }\n}\nclass B extends A {\n    // 额外的字段、构造函数和其他方法\n\n    @Override\n    public boolean equals(Object obj) {\n        if (this == obj) {\n            return true;\n        }\n        if (obj instanceof B) { // 检查参数是否是B类的实例\n            // 当obj是B类的实例时才会返回true\n            return true;\n        }\n        return false;\n    }\n}\npublic class MyClass {\n\n    public static void main(String[] args) {\n\n        B b = new B();\n        A a = new A();\n\n        System.out.println(a.equals(b)); // true\n        System.out.println(b.equals(a)); // false\n    }\n}\n</pre>\n<h2>合规的代码示例</h2>\n<pre>\nclass A {\n    // 字段、构造函数和其他方法\n\n    @Override\n    public boolean equals(Object obj) {\n        if (this == obj) {\n            return true;\n        }\n        if (obj instanceof A) { // 检查参数是否是A类的实例\n            // 当obj是A类的实例时才会返回true\n            return true;\n        }\n        return false;\n    }\n}\nclass B extends A {\n    // 额外的字段、构造函数和其他方法\n\n    @Override\n    public boolean equals(Object obj) {\n        if (this == obj) {\n            return true;\n        }\n        if (obj instanceof B || obj instanceof A) {\n            // A类和B类都对A或者B类型进行instanceof类型检查\n            return true;\n        }\n        return false;\n    }\n}\npublic class MyClass {\n\n    public static void main(String[] args) {\n\n        B b = new B();\n        A a = new A();\n\n        System.out.println(a.equals(b)); // true\n        System.out.println(b.equals(a)); // true\n    }\n}\n</pre>\n"}, "findbugs:DMI_EMPTY_DB_PASSWORD": {"title": "安全性 - 数据库未设置访问密码", "htmlDesc": "<p>这段代码使用空密码或空白密码创建了一个数据库连接。这表明数据库没有受到密码的保护。</p>\n<h2>不合规的代码示例</h2>\n<pre>\npublic class Example {\n    public Connection createConnection() {\n        String url = \"**************************************\";\n        String username = \"root\";\n        String password = \"\"; // 空密码，不合规\n        \n        try {\n            return DriverManager.getConnection(url, username, password);\n        } catch (SQLException e) {\n            e.printStackTrace();\n            return null;\n        }\n    }\n}\n</pre>\n<h2>合规的代码示例</h2>\n<p>修复方法是使用一个有效的、安全的密码来保护数据库。且避免硬编码密码。</p>\n<pre>\npublic class Example {\n    public Connection createConnection() {\n        String url = \"**************************************\";\n        String username = \"root\";\n        String password = getPasswordFromSecureStorage(); // 从安全存储中获取密码\n        \n        try {\n            return DriverManager.getConnection(url, username, password);\n        } catch (SQLException e) {\n            e.printStackTrace();\n            return null;\n        }\n    }\n    \n    private String getPasswordFromSecureStorage() {\n        // 从安全存储中获取密码的逻辑\n        // 可以使用配置文件、环境变量或密钥管理系统等安全存储\n    }\n}\n</pre>\n"}, "squid:S20002": {"title": "toMap Key冲突检查", "htmlDesc": "<p>toMap Key冲突检查</p>"}, "findbugs:WMI_WRONG_MAP_ITERATOR": {"title": "性能 - 使用 keySet 迭代器而不是 EntrySet 迭代器导致效率低下", "htmlDesc": "<p>这个方法通过使用从 keySet 迭代器检索到的键来访问 Map 元素的值，更高效的做法是使用 entrySet 上的迭代器，以避免 Map.get(key) 的查找操作。</p>\n<h2>不合规的代码示例</h2>\n<pre>\npublic void processMap(Map<String, Integer> map) {\n    Iterator<String> iterator = map.keySet().iterator();\n    while (iterator.hasNext()) {\n        String key = iterator.next();\n        Integer value = map.get(key);   // 不合规\n        System.out.println(\"Value for key \" + key + \": \" + value);\n    }\n}\n</pre>\n<h2>合规的代码示例</h2>\n<p>修复方法是改为使用 entrySet() 方法获取到 map 的元素集合，并创建一个元素的迭代器。通过使用迭代器，我们可以直接访问元素的键和值，避免了额外的键查找操作。</p>\n<pre>\npublic void processMap(Map<String, Integer> map) {\n    Iterator<Map.Entry<String, Integer>> iterator = map.entrySet().iterator();\n    while (iterator.hasNext()) {\n        Map.Entry<String, Integer> entry = iterator.next();\n        String key = entry.getKey();\n        Integer value = entry.getValue();\n        System.out.println(\"Value for key \" + key + \": \" + value);\n    }\n}\n</pre>\n"}, "squid:custom:LockInTryBlockChecker": {"title": "规范——禁止在try代码块内部执行加锁逻辑", "htmlDesc": "<p>【强制】在使用阻塞等待获取锁的方式中，必须在try代码块之外，并且在加锁方法与try代码块之间没有任何可能抛出异常的方法调用，避免加锁成功后，在finally中无法解锁。</p>\n<h2>不合规的代码示例</h2>\n<pre>\npublic void func(String[] args) {\n  try {\n    doSomething();\n    // 禁止在try内部加锁\n    lock.lock();\n    doOtherthings();\n  } finally {\n    lock.unlock();\n  }\n}\n</pre>\n<h2>合规的代码示例</h2>\n<pre>\npublic void func2(String[] args) {\n  lock.lock();\n  try {\n    doSomething();\n    doOtherthings();\n  } finally {\n    lock.unlock();\n  }\n}\n</pre>"}, "squid:custom:CountDownLatchAwaitChecker": {"title": "请使用CountDownLatch带超时参数的await方法", "htmlDesc": "<h2>检查await参数不为空来判断是否设置超时时间</h2>"}, "findbugs:SA_FIELD_SELF_ASSIGNMENT": {"title": "正确性 - 字段自我赋值", "htmlDesc": "<p>方法中将某个字段值赋给变量自己，此操作没有意义，可能代表着逻辑或拼写错误。</p>\n<h2>不合规的代码示例</h2>\n<pre>\nint x;\npublic void foo() {\n    x = x;          // 无意义，不合规\n}\n</pre>\n<h2>合规的代码示例</h2>\n<p>修复方法是仔细检查代码的逻辑和拼写问题，移除无用方法或赋值语句。</p>\n<pre>\nint x;\npublic void foo() {\n\n}\n</pre>"}, "squid:custom:CollectionToArrayParameterChecker": {"title": "规范——集合转数组时请使用toArray(new T[0])或toArray(T[]::new)", "htmlDesc": "<p>【强制】使用集合转数组的方法，必须使用集合的toArray(T[] array)，传入的是类型完全一致、长度为0的空数组；在JDK 11及以上版本可以使用T[]:new方式创建空数组。</p>\n<h2>不合规的代码示例</h2>\n<pre>\nList&lt;Integer&gt; list = new ArrayList<>(List.of(1, 2, 3));\n// 禁止强制类型转换\nInteger[] array = (Integer[]) list.toArray();\n\n\nList&lt;Integer&gt; list = new ArrayList<>(List.of(1, 2, 3));\n// 大小统一指定0\nInteger[] array = list.toArray(new Integer[8]);\n\n// 大小统一指定0\nInteger[] array = list.toArray(new Integer[list.size()]);\n</pre>\n<h2>合规的代码示例</h2>\n<pre>\nList&lt;Object&gt; list = new ArrayList<>();\n// 允许本身就是要使用 Object[]\nObject[] array = list.toArray();\n\n\nList&lt;Integer&gt; list = new ArrayList<>(List.of(1, 2, 3));\n// 大小统一指定0\nInteger[] array = list.toArray(new Integer[0]);\n\n// 可以使用 Integer[]::new\nInteger[] array = list.toArray(Integer[]::new);\n</pre>"}, "findbugs:BC_IMPOSSIBLE_DOWNCAST_OF_TOARRAY": {"title": "正确性——不可能的向下类型转换", "htmlDesc": "<p>此代码将调用 toArray() 方法返回的集合结果强制转换为比 Object[] 更具体的类型，这通常会导致 ClassCastException 异常。\n\n几乎所有集合的 toArray() 方法都返回 Object[]。实际上，它们无法返回其他类型的数组，因为 Collection 对象没有对集合的声明泛型类型的引用。\n\n从集合中获取指定类型的数组的正确方法是使用 c.toArray(new String[]); 或 c.toArray(new String[c.size()]);（后者稍微更高效）。\n\n这里有一个常见的/已知的例外情况。通过 Arrays.asList(...) 返回的列表的 toArray() 方法将返回一个具有协变类型的数组。例如，Arrays.asArray(new String[] { \"a\" }).toArray() 将返回 String[] 类型的数组。SpotBugs 尝试检测并抑制这种情况，但可能会有遗漏。</p>\n<h2>不合规的代码示例</h2>\n<pre>\npublic static void main(String[] args) {\n  Collection<String> collection = new ArrayList<>();\n  collection.add(\"Hello\");\n  collection.add(\"World\");\n\n  String[] array = getAsArray(collection);\n  for (String s : array) {\n    System.out.println(s);\n  }\n}\n\nprivate static String[] getAsArray(Collection<String> c) {\n  return (String[]) c.toArray(); // ClassCastException\n}\n</pre>\n<h2>合规的代码示例</h2>\n<p>从集合中获取指定类型的数组的正确方法是使用 c.toArray(new String[]); 或 c.toArray(new String[c.size()]);</p>\n<pre>\nprivate static String[] getAsArray(Collection<String > collection) {\n  return collection.toArray(new String[0]);\n}\n</pre>\n"}, "findbugs:UUF_UNUSED_FIELD": {"title": "性能 - 从未被使用的字段", "htmlDesc": "<p>这个字段从未被使用。考虑从类中删除它。</p>\n<h2>不合规的代码示例</h2>\n<pre>\npublic class Example {\n    private int unusedField = 10;   // 该字段从未被使用，不合规\n\n    public void doSomething() {\n\n    }\n}\n</pre>\n<h2>合规的代码示例</h2>\n<p>修复方法是将未被使用的字段从类中删除。</p>\n<pre>\npublic class Example {\n    public void doSomething() {\n        // 删除了未被使用的字段 unusedField\n    }\n}\n</pre>\n"}, "findbugs:NP_ALWAYS_NULL": {"title": "正确性 - 空指针解引用", "htmlDesc": "<p>此处代码对空指针进行解引用。当程序执行到此处时将会导致空指针异常。</p>\n<h2>不合规的代码示例</h2>\n<pre>\npublic class Main {\n    public static void main(String[] args) {\n        String msg = null;\n        // do other things\n        System.out.println(msg.hashCode()); // msg为空，导致NullPointerException\n    }\n}\n</pre>\n<h2>合规的代码示例</h2>\n<p>对可能为空的对象引用进行解引用之前必须判空。</p>\n<pre>\npublic class Main {\n    public static void main(String[] args) {\n        String msg = null;\n        // do other things\n        if (msg != null) {  // 先判空\n            System.out.println(msg.hashCode()); \n        }\n    }\n}\n</pre>\n"}, "squid:custom:MainSiteIpV6ForbiddenChecker": {"title": "主站——禁止使用不允许的获取ip方法", "htmlDesc": "<p>主站——禁止使用不允许的获取ip方法</p>\n<h2>不合规的代码示例</h2>\n<pre>\n    currentIp.ipInLong();\t// 不合规\n</pre>\n<h2>合规的代码示例</h2>\n<pre>\n    public static IpResult getIpResult(@Nullable EndpointIp endpointIp)// 合规\n</pre>"}, "squid:custom:CaseAddBreakChecker": {"title": "Switch最后一个case最好也加上break或者return", "htmlDesc": "<p>Switch最后一个case也要写好break或者return，因为后来者再新增case时，不会记得看上面的case是否return了，会导致上一个case执行后会接着执行新增的case</p>\n<h2>不合规的代码示例</h2>\n<pre>\n        switch (command) {\n            case \"apply\":\n                task.migratingDataApply(from, to);\n                break;\n            case \"retry\":\n                task.retryExchangeRecord(from, to);// 不合规\n            default:\n                throw new IllegalArgumentException(\"unknown command \" + command);\n        }\n</pre>\n<h2>合规的代码示例</h2>\n<pre>\n        switch (command) {\n            case \"apply\":\n                task.migratingDataApply(from, to);\n                break;\n            case \"retry\":\n                task.retryExchangeRecord(from, to);\n                break; //合规\n            default:\n                throw new IllegalArgumentException(\"unknown command \" + command);\n        }\n</pre>"}, "findbugs:UR_UNINIT_READ": {"title": "正确性 - 构造函数中字段的未初始化读取", "htmlDesc": "<p>构造函数读取了一个还没有被赋值的字段。这通常是因为程序员错误地使用了类的字段而不是构造函数的参数导致的。</p>\n<h2>不合规的代码示例</h2>\n<pre>\npublic class Person {\n    private String name;\n    private int age;\n\n    public Person(String _name, int _age) {\n        this.name = _name;\n        this.age = age; // 错误的使用了类的字段而不是参数，不合规\n    }\n}\n</pre>\n<h2>合规的代码示例</h2>\n<p>修复这个问题的方法是将构造函数中的参数正确地赋值给对应的字段。</p>\n<pre>\npublic class Person {\n    private String name;\n    private int age;\n\n    public Person(String _name, int _age) {\n        this.name = _name;\n        this.age = _age; // 使用构造方法的参数，合规\n    }\n}\n</pre>\n"}, "squid:custom:AdPostMethodChecker": {"title": "商业化——Controller方法禁止同时使用@RequestParam和@PostMapping/@RequestMapping(method = RequestMethod.POST) 注解", "htmlDesc": "<h2>Controller方法禁止同时使用@RequestParam和@PostMapping/@RequestMapping(method = RequestMethod.POST)注解<b>（商业化专用）</b>.</h2>\n<p>当引用了公司的infra passport包同时打开了KTrace的Filter后，如果代码里用@RequestParams注解去接POST参数，可能会导致乱码问题</p>\n<h2>不合规的代码示例</h2>\n<pre>\n    @PostMapping(\"/example1\")\n    public void exampleMethod1(@RequestParam String param) {\n        // 方法体\n    }\n\n    // 这个方法也应该触发规则\n    @RequestMapping(value = \"/example2\", method = RequestMethod.POST)\n    public void exampleMethod2(@RequestParam String param) {\n        // 方法体\n    }\n</pre>\n<h2>合规的代码示例</h2>\n<pre>\n    @GetMapping(\"/example3\")\n    public void exampleMethod3(@RequestParam String param) {\n        // 方法体\n    }\n</pre>"}, "findbugs:RV_RETURN_VALUE_IGNORED": {"title": "正确性 - 方法返回值被忽略", "htmlDesc": "<p>该方法的返回值应该进行检查。产生此警告的一个常见原因是在一个不可变对象上调用方法，错误地认为它会更新该对象。</p>\n<h2>不合规的代码示例</h2>\n<pre>\nString dateString = \"  hello world!  \";\ndateString.trim();          // 忽略返回值，由于String不可变，并不会对 dateString 内容进行改变，不合规\nSystem.out.println(dateString);\n</pre>\n<h2>合规的代码示例</h2>\n<pre>\nString dateString = getHeaderField(name);\ndateString = dateString.trim();     // 接收返回值，合规\nSystem.out.println(dateString);\n</pre>\n"}, "squid:custom:LogParameterUseStringConcatChecker": {"title": "规范——禁止使用字符串拼接构造日志内容", "htmlDesc": "<p>【强制】优先使用结构化日志，或使用占位符方式组装日志，禁止字符串直接拼接。</p>\n<h2>不合规的代码示例</h2>\n<pre>\npublic static void main(String[] args) {\n  try {\n    doSomething();\n  } catch (Exception e) {\n    // 禁止使用 + 拼接\n    log.info(\"任务失败！args = \" + args);\n  }\n}\n</pre>\n<h2>合规的代码示例</h2>\n<pre>\npublic static void main(String[] args) {\n  try {\n    doSomething();\n  } catch (Exception e) {\n    log.info(\"任务失败！args = {}\", args);\n  }\n}\n</pre>"}, "findbugs:URF_UNREAD_FIELD": {"title": "性能 - 从未被读取的字段", "htmlDesc": "<p>这个字段从未被读取。考虑从类中删除它。</p>\n<h2>不合规的代码示例</h2>\n<pre>\npublic class Example {\n    private int unreadField = 10;   // 该字段只被赋值，从未被读取，不合规\n\n    public void setField() {\n        this.unreadField = 20;\n    }\n}\n</pre>\n<h2>合规的代码示例</h2>\n<p>修复方法是将未被读取的字段从类中删除。</p>\n<pre>\npublic class Example {\n    public void doSomething() {\n        // 删除了未被读取的字段 unusedField\n    }\n}\n</pre>\n"}, "findbugs:SIC_THREADLOCAL_DEADLY_EMBRACE": {"title": "正确性 - 非静态内部类和threadlocal变量致命关联。", "htmlDesc": "<p>这个类是一个内部类，但应该是一个静态内部类。现在的情况是，内部类和外部类中的threadLocal变量之间存在严重的关联危险。因为内部类不是静态的，它会保留对外部类的引用。如果threadLocal变量包含对内部类实例的引用，那么内部和外部实例都将可以访问，并且不会被垃圾回收。</p>\n<h2>不合规的代码示例</h2>\n<pre>\npublic class OuterClass {\n    private ThreadLocal<InnerClass> threadLocal = new ThreadLocal<>();\n\n    public void someMethod() {\n        InnerClass innerClass = new InnerClass();\n        threadLocal.set(innerClass); // 持有对InnerClass对象的引用\n    }\n\n    public class InnerClass {   // 非静态内部类\n        // Inner class implementation\n    }\n}\n</pre>\n<h2>合规的代码示例</h2>\n<pre>\npublic class OuterClass {\n    private ThreadLocal<InnerClass> threadLocal = new ThreadLocal<>();\n\n    public void someMethod() {\n        InnerClass innerClass = new InnerClass();\n        threadLocal.set(innerClass);\n    }\n\n    public static class InnerClass { // 静态内部类\n        // Inner class implementation\n    }\n}\n</pre>\n"}, "squid:custom:KspayAbsoluteValueCalculationChecker": {"title": "支付——string.hashcode()结果可能为负值，应取绝对值来进行后续计算操作", "htmlDesc": "<p>支付——string.hashcode()结果可能为负值，应取绝对值来进行后续计算操作</p>\n<h2>不合规的代码示例</h2>\n<pre>\n    public static long getShardByAccountId1(String accountId) {\n        return Math.abs(accountId.hashCode()) % WALLET_TRANSACTION_SHARD_COUNT;//不合规\n    }\n\n    public static long getShardByAccountId2(String accountId) {\n        return accountId.hashCode() % WALLET_TRANSACTION_SHARD_COUNT;//不合规\n    }\n</pre>\n<h2>合规的代码示例</h2>\n<pre>\n    public static long getShardByAccountId0(String accountId) {\n        return Math.abs((long) accountId.hashCode()) % WALLET_TRANSACTION_SHARD_COUNT;//合规\n    }\n</pre>"}, "findbugs:NP_NONNULL_RETURN_VIOLATION": {"title": "正确性 - 方法可能返回 null，但声明为 @Nonnull", "htmlDesc": "<p>此方法可能返回空值，但该方法（或覆盖的超类方法）被声明为返回@Nonnull</p>\n<h2>不合规的代码示例</h2>\n<pre>\nclass A {\n    String name;\n    \n    public String getName() {\n        return name;\n    }\n}\npublic class ExampleClass {\n    @Nonnull\n    public String getValue(A a) {\n        // 返回值可能为null\n        return a.getName();\n    }\n}\n</pre>\n<h2>合规的代码示例</h2>\n<p>对可能的空值进行特殊处理，或 如果某些情况下返回空值符合预期，则将方法声明改为@Nullable</p>\n<pre>\npublic class ExampleClass {\n    @Nonnull\n    public String getValue(A a) {\n        // 返回值可能为null\n        String ans = a.getName();\n        if (ans == null) {\n            throw new NullPointerException(); // 特殊处理\n        }\n        return ans;\n    }\n}\n\npublic class ExampleClass {\n    @Nullable // 允许返回空值\n    public String getValue(A a) {\n        // 返回值可能为null\n        return a.getName();\n    }\n}\n</pre>\n"}, "squid:custom:CatchAndIgnoreExceptionChecker": {"title": "规范——捕获并被忽略的异常必须注释写明原因", "htmlDesc": "<p>【强制】捕获并被忽略的异常，要注释写明原因。</p>\n<h2>不合规的代码示例</h2>\n<pre>\n@Autowired\nprivate UserService userService;\n\npublic void aMethodMayProduceNpe(Object object) {\n  try {\n    userService.save(object);\n  } catch (DuplicateKeyException e) { // 必须写明忽略原因\n  }\n}\n</pre>\n<h2>合规的代码示例</h2>\n<pre>\n@Autowired\nprivate UserService userService;\n\npublic void aMethodMayProduceNpe(Object object) {\n  try {\n    userService.save(object);\n  } catch (DuplicateKeyException ignore) { // 异常名为 ignore、impossible、expected 时允许不写注释\n  }\n}\n\npublic void bMethodMayProduceNpe(Object object) {\n  try {\n    userService.save(object);\n  } catch (DuplicateKeyException e) {\n    // The reason for this exception can be ignored\n  }\n}\n</pre>"}}