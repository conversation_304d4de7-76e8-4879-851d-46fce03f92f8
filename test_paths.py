#!/usr/bin/env python3
"""
测试路径配置和文件访问
"""

import os
import json
from config import BASE_SOURCE_PATH, get_full_path, validate_base_path, validate_data_files

def test_base_path():
    """测试基础路径"""
    print("🔍 测试基础路径配置:")
    print(f"基础路径: {BASE_SOURCE_PATH}")
    
    if os.path.exists(BASE_SOURCE_PATH):
        print("✅ 基础路径存在")
        
        # 列出一些文件作为验证
        try:
            files = os.listdir(BASE_SOURCE_PATH)[:10]  # 只显示前10个
            print(f"📁 目录内容 (前10个): {files}")
        except Exception as e:
            print(f"❌ 无法读取目录内容: {e}")
    else:
        print("❌ 基础路径不存在")
        return False
    
    return True

def test_data_files():
    """测试数据文件"""
    print("\n📋 测试数据文件:")
    
    # 测试issues.json
    issues_file = "data/issues.json"
    print(f"Issues文件: {issues_file}")
    
    if os.path.exists(issues_file):
        print("✅ Issues文件存在")
        try:
            with open(issues_file, 'r', encoding='utf-8') as f:
                issues_data = json.load(f)
            print(f"📊 包含 {len(issues_data)} 个文件的issues")
            
            # 显示第一个文件作为示例
            if issues_data:
                first_file = list(issues_data.keys())[0]
                first_issues = issues_data[first_file]
                print(f"📄 示例文件: {first_file}")
                print(f"🐛 问题数量: {len(first_issues)}")
        except Exception as e:
            print(f"❌ 读取Issues文件失败: {e}")
            return False
    else:
        print("❌ Issues文件不存在")
        return False
    
    # 测试rules.json
    rules_file = "data/rules_origin.json"
    print(f"\nRules文件: {rules_file}")
    
    if os.path.exists(rules_file):
        print("✅ Rules文件存在")
        try:
            with open(rules_file, 'r', encoding='utf-8') as f:
                rules_data = json.load(f)
            print(f"📊 包含 {len(rules_data)} 个规则定义")
        except Exception as e:
            print(f"❌ 读取Rules文件失败: {e}")
            return False
    else:
        print("❌ Rules文件不存在")
        return False
    
    return True

def test_sample_file_access():
    """测试示例文件访问"""
    print("\n🔧 测试文件访问:")
    
    # 从issues.json中获取一个示例文件
    try:
        with open("data/issues.json", 'r', encoding='utf-8') as f:
            issues_data = json.load(f)
        
        if not issues_data:
            print("❌ Issues数据为空")
            return False
        
        # 选择第一个文件进行测试
        sample_file = list(issues_data.keys())[0]
        print(f"📄 测试文件: {sample_file}")
        
        # 测试路径转换
        full_path = get_full_path(sample_file)
        print(f"🔗 完整路径: {full_path}")
        
        # 测试文件是否存在
        if os.path.exists(full_path):
            print("✅ 文件存在")
            
            # 尝试读取文件内容
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                print(f"📝 文件大小: {len(content)} 字符")
                print(f"📝 文件行数: {len(content.splitlines())} 行")
                
                # 显示前几行作为验证
                lines = content.splitlines()[:5]
                print("📄 文件前5行:")
                for i, line in enumerate(lines, 1):
                    print(f"  {i}: {line[:80]}{'...' if len(line) > 80 else ''}")
                
            except Exception as e:
                print(f"❌ 读取文件内容失败: {e}")
                return False
        else:
            print("❌ 文件不存在")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    return True

def test_agent_validation():
    """测试Agent验证功能"""
    print("\n🤖 测试Agent验证:")
    
    try:
        validate_base_path()
        print("✅ 基础路径验证通过")
    except RuntimeError as e:
        print(f"❌ 基础路径验证失败: {e}")
        return False
    
    try:
        validate_data_files()
        print("✅ 数据文件验证通过")
    except RuntimeError as e:
        print(f"❌ 数据文件验证失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("🧪 路径配置和文件访问测试")
    print("=" * 50)
    
    tests = [
        ("基础路径测试", test_base_path),
        ("数据文件测试", test_data_files),
        ("示例文件访问测试", test_sample_file_access),
        ("Agent验证测试", test_agent_validation)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！Agent可以正常访问文件。")
    else:
        print("⚠️ 部分测试失败，请检查配置。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
